layout {
    tab name="editor" {
        pane split_direction="vertical" {
            pane {
                cwd "~/Github/viclass/viclass"
                command "yarn"
                args "watch-editor.core"
                start_suspended true
            }
            pane {
                cwd "~/Github/viclass/viclass"
                command "yarn"
                args "watch-editor.coord"
                start_suspended true
            }
        }
        pane split_direction="vertical" {
            pane {
                cwd "~/Github/viclass/viclass"
                command "yarn"
                args "watch-editor.geo"
                start_suspended true
            }
            pane {
                cwd "~/Github/viclass/viclass"
                command "yarn"
                args "watch-editorui.geo"
                start_suspended true
            }
        }
        pane split_direction="vertical" {
            pane {
                cwd "~/Github/viclass/viclass"
                command "yarn"
                args "watch-mfe"
                start_suspended true
            }
        }
        pane split_direction="vertical" {
            pane {
                cwd "~/Github/viclass/viclass"
                command "yarn"
                args "watch-portal.common"
                start_suspended true
            }
            pane {
                cwd "~/Github/viclass/viclass"
                command "yarn"
                args "watch-portal.homepage"
                start_suspended true
            }
            pane {
                cwd "~/Github/viclass/viclass"
                command "yarn"
                args "watch-portal.classroom"
                start_suspended true
            }
        }
    }
    tab name="nginx and build-all" {
        pane split_direction="vertical" {
            pane {
                cwd "~/Github/viclass/nginx-devtool"
                command "bash"
                args "-c" "yarn down && yarn up"
                start_suspended true
            }
            pane {
                cwd "~/Github/viclass/viclass"
                command "bash"
                args "-c" "yarn && yarn build-all"
                start_suspended true
            }
        }
        pane split_direction="vertical" {
            pane {
                cwd "~/Github/viclass/nginx"
                command "bash"
                args "-c" "yarn down && yarn up"
                start_suspended true
            }
        }
    }
    tab name="backend" {
        pane split_direction="vertical" {
            pane {
                cwd "~/Github/viclass"
                command "./gradlew"
                args ":portal.backend:run"
                start_suspended true
            }
            pane {
                cwd "~/Github/viclass"
                command "./gradlew"
                args ":portal.user:run"
                start_suspended true
            }
        }
        pane split_direction="vertical" {
            pane {
                cwd "~/Github/viclass"
                command "./gradlew"
                args ":editor.geo:run"
                start_suspended true
            }
            pane {
                cwd "~/Github/viclass"
                command "./gradlew"
                args ":editor.freedrawing:run"
                start_suspended true
            }
        }
        pane split_direction="vertical" {
            pane {
                cwd "~/Github/viclass"
                command "./gradlew"
                args ":portal.beta:run"
                start_suspended true
            }
            pane {
                cwd "~/Github/viclass"
                command "./gradlew"
                args ":portal.notification:run"
                start_suspended true
            }
        }
    }
}
