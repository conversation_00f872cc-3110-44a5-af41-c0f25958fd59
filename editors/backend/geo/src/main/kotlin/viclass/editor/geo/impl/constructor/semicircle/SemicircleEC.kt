package viclass.editor.geo.impl.constructor.semicircle

import kotlin.reflect.KClass
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Semicircle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generateLowercaseName
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.SemicircleImpl

/**
 *
 * <AUTHOR>
 */
@Singleton
class SemicircleEC : ElementConstructor<Semicircle> {

    override fun outputType(): KClass<Semicircle> {
        return Semicircle::class
    }

    private enum class CGS {
        WithTwoEndpoints
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.WithTwoEndpoints.name)
                    .hints()
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-StartPoint"
                    )
                    .constraintDepends(
                        1,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf(0),
                        listOf("NameOfPoint"),
                        "tpl-EndPoint"
                    )
                    .constraintOptional(
                        2,
                        ConstraintParamDefManager.instance()["aName"]!!,
                        listOf(0, 1),
                        listOf("NameOfPoint"),
                        "tpl-CenterPoint"
                    )
                    .build(),
            )
            .elTypes(Semicircle::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Semicircle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.WithTwoEndpoints -> {
                Validations.validateNumConstraints(c, 2)
                constructWithTwoEndpoints(doc, inputName, c)
            }
        }
    }

    private fun constructWithTwoEndpoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction,
    ): ConstructionResult<Semicircle> {
        val pStart = extractFirstPossible<ElementExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx).result.result()
            ?: throw ConstructionException("not found start point")
        val pEnd = extractFirstPossible<ElementExtraction<Point>>(doc, PK_Name, c.params[1], c.ctIdx).result.result()
            ?: throw ConstructionException("not found end point")

        // Calculate center point from the two endpoints
        val centerX = (pStart.x + pEnd.x) / 2
        val centerY = (pStart.y + pEnd.y) / 2
        
        // Get center point name from aName parameter or generate new one
        var centerPointName: String? = null
        
        c.params.forEach { p ->
            when (p.paramDef.id) {
                "aName" -> {
                    centerPointName = extractFirstPossible<StringExtraction>(doc, PK_Name, p, c.ctIdx).result
                }
            }
        }
        
        // Use provided name or generate new one
        val finalCenterPointName = if (centerPointName.isNullOrBlank()) {
            generatePointName(doc)
        } else {
            centerPointName
        }
        
        // Create center point
        val pCenter = PointImpl(doc, finalCenterPointName, centerX, centerY)

        // Use inputName for semicircle if provided, otherwise generate new one
        val finalSemicircleName = inputName ?: generateLowercaseName(doc, arrayListOf())
        val semicircle = SemicircleImpl(doc, finalSemicircleName, pCenter, pStart, pEnd)

        val cr = ConstructionResultImpl<Semicircle>()
        cr.setResult(semicircle)
        cr.addDependency(pStart, emptyList(), true)
        cr.addDependency(pEnd, emptyList(), true)
        cr.addDependency(pCenter, listOf(pStart, pEnd), true)

        return cr
    }
}
