package viclass.editor.geo.impl.constructor.polygon

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementExtraction
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.elements.RegularPolygon
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.RegularPolygonImpl
import kotlin.math.*
import kotlin.reflect.KClass

/**
 * Element Constructor for Regular Polygons.
 *
 * This class provides methods to construct regular polygons from different sets of parameters,
 * such as from a center and a vertex, or from a chord defined by two existing vertices.
 *
 * <AUTHOR>
 */
@Singleton
class RegularPolygonEC : ElementConstructor<RegularPolygon> {
    override fun outputType(): KClass<RegularPolygon> {
        return RegularPolygon::class
    }

    private enum class CGS { FromCenterAndPoint, FromCenterAndMidPoint, FromChord }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromChord.name)
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.FromCenterAndPoint.name)
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-CenterPointOfPolygon"
                    )
                    .constraint(
                        1,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-Point"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.FromCenterAndMidPoint.name)
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-CenterPointOfPolygon"
                    )
                    .constraint(
                        1,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-MiddlePointIs"
                    )
                    .build(),
            )
            .elTypes(RegularPolygon::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<RegularPolygon> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromChord -> {
                Validations.validateNumConstraints(c, 0)
                constructFromChord(doc, inputName, c)
            }
            CGS.FromCenterAndPoint -> {
                Validations.validateNumConstraints(c, 2)
                constructFromCenterAndPoint(doc, inputName, c)
            }
            CGS.FromCenterAndMidPoint -> {
                Validations.validateNumConstraints(c, 2)
                constructFromCenterAndMidPoint(doc, inputName, c)
            }
        }
    }

    private fun constructFromCenterAndMidPoint(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<RegularPolygon> {
        val polygonName = inputName ?: throw ConstructionException("Tên đa giác phải được cung cấp cho phương thức tạo này.")

        // 1. Extract and validate input parameters
        val centerExtraction = extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val pCenter = centerExtraction.result.result()!!

        val midPointExtraction = extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val pMid = midPointExtraction.result.result()!!

        if (pCenter.coordinates() == pMid.coordinates()) {
            throw ConstructionException("Tâm và trung điểm của cạnh không được trùng nhau.")
        }

        // 2. Parse the polygon name to get all vertex names and the edge count.
        val allVertexNames = NamePattern.extractPointName(Polygon::class, polygonName)
        val edgeCount = allVertexNames.size
        if (edgeCount < 3) {
            throw ConstructionException("Đa giác đều phải có ít nhất 3 đỉnh.")
        }

        // 3. Calculate geometric parameters
        val n = edgeCount.toDouble()
        // The distance from the center to the midpoint is the apothem (h)
        val h = Distances.of(pCenter, pMid)
        // The angle at the center subtended by half a side is (PI / n)
        val halfAngle = PI / n
        // The radius of the circumscribed circle is h / cos(pi/n)
        val R = h / cos(halfAngle)

        // 4. Determine the starting angle for generation
        // The angle of the apothem vector (center to midpoint)
        val angleMid = atan2(pMid.y - pCenter.y, pMid.x - pCenter.x)
        // The angle of the "zeroth" vertex is the midpoint angle minus the half-angle of the side.
        // This places the first vertex correctly relative to the midpoint of the first side.
        val angleV0 = angleMid - halfAngle
        // The angle step for counter-clockwise generation
        val angleStep = 2.0 * PI / n

        // 5. Build the list of final vertices
        val cr = ConstructionResultImpl<RegularPolygon>()
        cr.addDependencies(listOf(pCenter, pMid), true)

        val finalVertices = mutableListOf<Point>()
        for (i in 0 until edgeCount) {
            val currentName = allVertexNames[i]
            // Check if a point with this name already exists in the document.
            val existingPoint = doc.findElementByName(currentName, Point::class, c.ctIdx)

            val point: Point
            if (existingPoint != null) {
                // If the point already exists, reuse it.
                point = existingPoint
                cr.addDependency(existingPoint, listOf(), true)
            } else {
                // If the point does not exist, create a new one.
                val currentAngle = angleV0 + i * angleStep
                val pointX = pCenter.x + R * cos(currentAngle)
                val pointY = pCenter.y + R * sin(currentAngle)
                point = PointImpl(doc, currentName, pointX, pointY)
                // Each new vertex depends on the initial center and midpoint
                cr.addDependency(point, listOf(pCenter, pMid), false)
            }
            finalVertices.add(point)
        }

        // 6. Create the polygon and return the result
        val polygon = RegularPolygonImpl(doc, polygonName, finalVertices)
        cr.setResult(polygon)

        return cr
    }

    private fun constructFromCenterAndPoint(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<RegularPolygon> {
        // The polygon name MUST be provided. It defines the number of sides and all vertex names.
        val polygonName = inputName ?: throw ConstructionException("Tên đa giác phải được cung cấp cho phương thức tạo này.")

        // 1. Extract input parameters
        val centerExtraction = extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val pCenter = centerExtraction.result.result()!!

        val vertexExtraction = extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val pVertex = vertexExtraction.result.result()!!

        // 2. Parse polygon name and validate inputs
        val allVertexNames = NamePattern.extractPointName(Polygon::class, polygonName)
        val edgeCount = allVertexNames.size

        if (edgeCount < 3) {
            throw ConstructionException("Đa giác đều phải có ít nhất 3 cạnh.")
        }
        if (pCenter.coordinates() == pVertex.coordinates()) {
            throw ConstructionException("Điểm tâm và một đỉnh không được trùng nhau.")
        }

        // Find the index of the provided vertex.
        val pVertexIndex = allVertexNames.indexOf(pVertex.name)
        if (pVertexIndex == -1) {
            throw ConstructionException("Điểm đỉnh '${pVertex.name}' không phải là một phần của tên đa giác '$polygonName'.")
        }

        // 3. Calculate geometric values
        val n = edgeCount.toDouble()
        val angleStep = 2.0 * PI / n // Angle for counter-clockwise generation
        val radius = pCenter.coordinates().distance(pVertex.coordinates())

        // 4. Determine the starting angle for generation
        // The angle of the provided vertex relative to the center.
        val pVertexAngle = atan2(pVertex.y - pCenter.y, pVertex.x - pCenter.x)
        // Back-calculate the angle for the "zeroth" vertex based on the provided vertex's index.
        val angleV0 = pVertexAngle - pVertexIndex * angleStep

        // 5. Build the list of final vertices
        val cr = ConstructionResultImpl<RegularPolygon>()
        cr.addDependencies(listOf(pCenter, pVertex), true)

        val finalVertices = mutableListOf<Point>()
        for (i in 0 until edgeCount) {
            val currentName = allVertexNames[i]
            // Check if a point with this name already exists in the document.
            val existingPoint = doc.findElementByName(currentName, Point::class, c.ctIdx)

            val point: Point
            if (existingPoint != null) {
                // If the point already exists, reuse it.
                point = existingPoint
                // If this existing point is not the initial input vertex,
                // it's also a hard dependency.
                if (existingPoint != pVertex) {
                    cr.addDependency(existingPoint, listOf(), true)
                }
            } else {
                // If the point does not exist, create a new one.
                val currentAngle = angleV0 + i * angleStep
                val pointX = pCenter.x + radius * cos(currentAngle)
                val pointY = pCenter.y + radius * sin(currentAngle)

                point = PointImpl(doc, currentName, pointX, pointY)
                // The newly created point depends on the center and the original vertex.
                cr.addDependency(point, listOf(pCenter, pVertex), false)
            }
            finalVertices.add(point)
        }

        // 6. Create the polygon and return the result
        val polygon = RegularPolygonImpl(doc, polygonName, finalVertices)
        cr.setResult(polygon)

        return cr
    }

    private fun constructFromChord(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<RegularPolygon> {
        val polygonName = inputName ?: throw ConstructionException("Tên đa giác phải được cung cấp cho phương thức tạo này.")

        // 1. Parse the polygon name to get all vertex names and the edge count.
        val allVertexNames = NamePattern.extractPointName(Polygon::class, polygonName)
        val edgeCount = allVertexNames.size
        if (edgeCount < 3) {
            throw ConstructionException("Đa giác đều phải có ít nhất 3 đỉnh.")
        }

        // 2. Find all existing vertices from the polygon's name and store them in a map for efficient lookup.
        val foundPointsMap = allVertexNames
            .mapNotNull { name -> doc.findElementByName(name, Point::class, c.ctIdx)?.let { name to it } }
            .toMap()

        // This construction method requires at least two vertices to exist to form a chord.
        if (foundPointsMap.size < 2) {
            throw ConstructionException("Phải có ít nhất hai đỉnh của đa giác ('$polygonName') đã tồn tại trong tài liệu cho phương thức tạo này.")
        }

        // Use the first two found vertices as the chord.
        val foundPoints = foundPointsMap.values.toList()
        val p1 = foundPoints[0]
        val p2 = foundPoints[1]

        if (p1.coordinates() == p2.coordinates()) {
            throw ConstructionException("Hai điểm được tìm thấy ('${p1.name}', '${p2.name}') không được trùng nhau.")
        }

        // 3. Find the indices of these two points within the full list of polygon vertices.
        val idx1 = allVertexNames.indexOf(p1.name)
        val idx2 = allVertexNames.indexOf(p2.name)

        // 4. Calculate geometric parameters.
        val n = edgeCount.toDouble()
        val chordDistance = Distances.of(p1, p2)

        // Ensure 'm' is the shorter distance around the polygon's perimeter.
        val m_dist = abs(idx1 - idx2)
        val m = min(m_dist, edgeCount - m_dist).toDouble()

        val alpha = m * PI / n

        if (abs(sin(alpha)) < DEFAULT_TOLERANCE) {
            throw ConstructionException("Các điểm và số cạnh đã cho tạo ra một cấu hình suy biến (các đỉnh thẳng hàng).")
        }

        val R = chordDistance / (2.0 * sin(alpha))
        val h = sqrt(max(0.0, R * R - (chordDistance / 2.0).pow(2)))

        // 5. Find the two possible centers for the circumscribed circle.
        val midPoint = Points.calculateCenterPoint(p1, p2)
        val chordVector = p1.coordinates().vectorTo(p2.coordinates())
        // center1 is on the "left" (CCW), center2 is on the "right" (CW)
        val perpVector = Vector3D.of(-chordVector.y, chordVector.x, 0.0).normalize()
        val center1 = midPoint.add(h, perpVector)
        val center2 = midPoint.subtract(h, perpVector)

        // 6. Select the correct center to ensure CLOCKWISE rotation from p1 to p2
        //    respecting the vertex order in the polygon's name.

        // The number of clockwise steps required to get from p1 to p2.
        val steps = (idx2 - idx1 + edgeCount) % edgeCount

        // Check the direction of the short-path rotation if we use center1.
        val vecC1P1 = center1.vectorTo(p1.coordinates())
        val vecC1P2 = center1.vectorTo(p2.coordinates())
        val crossProductZ = vecC1P1.x * vecC1P2.y - vecC1P1.y * vecC1P2.x

        val finalCenter = if (steps > edgeCount / 2.0) {
            // We need to travel the "long way" around (CW).
            // This means the "short way" must be counter-clockwise (CCW).
            // A positive crossProductZ means the short path around center1 is CCW, so we choose center1.
            if (crossProductZ > 0) center1 else center2
        } else {
            // We need to travel the "short way" around (CW).
            // A negative crossProductZ means the short path around center1 is CW, so we choose center1.
            if (crossProductZ < 0) center1 else center2
        }

        // 7. Calculate the angle of the first vertex (vertex 0) relative to the final center.
        // The angle step is negative to ensure CLOCKWISE generation.
        val angleStep = -2.0 * PI / n
        val angleP1 = atan2(p1.y - finalCenter.y, p1.x - finalCenter.x)
        val angleV0 = angleP1 - idx1 * angleStep

        // 8. Build the list of final vertices, creating new points where necessary.
        val cr = ConstructionResultImpl<RegularPolygon>()
        cr.addDependencies(listOf(p1, p2), true)

        val finalVertices = mutableListOf<Point>()
        for (i in 0 until edgeCount) {
            val currentName = allVertexNames[i]
            // Check if the point already exists using our pre-populated map.
            val existingPoint = foundPointsMap[currentName]

            val point: Point = if (existingPoint != null) {
                // If the point already exists, reuse it.
                if (existingPoint != p1 && existingPoint != p2) {
                    cr.addDependency(existingPoint, listOf(), true)
                }
                existingPoint
            } else {
                // If the point does not exist, create a new one.
                val angle = angleV0 + i * angleStep
                val pointX = finalCenter.x + R * cos(angle)
                val pointY = finalCenter.y + R * sin(angle)
                PointImpl(doc, currentName, pointX, pointY).also {
                    cr.addDependency(it, listOf(p1, p2), true)
                }
            }
            finalVertices.add(point)
        }

        // 9. Create the polygon and return the result.
        val polygon = RegularPolygonImpl(doc, polygonName, finalVertices)
        cr.setResult(polygon)

        return cr
    }
}