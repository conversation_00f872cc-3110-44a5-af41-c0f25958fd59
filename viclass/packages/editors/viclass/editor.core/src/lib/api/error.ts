/**
 * Definition of a set of errors that are common, and can be handled across the system
 */
export class ViErr extends Error {
    constructor(
        public override readonly message: string,
        public readonly raw?: Error
    ) {
        super(message, { cause: raw ?? new Error(message) });
    }

    getRootRaw() {
        if (this.raw) {
            if (this.raw instanceof ViErr) return this.raw.getRootRaw();
            else return this.raw;
        }
        return this;
    }
}

/**
 * General Critical Error. Our code should throw this error
 * if it knows that the error might potentially cause inconsistency after a particular
 * operation, but without knowing the exact cause.
 */
export class CriticalErr extends ViErr {}

export class NonCriticalErr extends ViErr {}

/**
 * All errors that we are unsure of the consequence after the API call.
 * For example, when error is a consequence of gateway timeout, it's possible that the state of
 * the document is no longer correct. A reload of page or other method of recovery, e.g. reload the doc, might
 * be necessary
 */
export class CriticalDocumentAPIErr extends CriticalErr {}

/**
 * All errors that DOESN'T change the document state (e.g. doesn't modify the document content)
 * For these types of errors, mostly displaying error message is OK.
 */
export class NonCriticalDocumentAPIErr extends NonCriticalErr {}

/**
 * Error related to initialization of editor, coordinator.
 * For example, editor cannot be added, coordinator cannot be started successfully
 * or classroom unable to load its content, etc...
 *
 */
export class InitializationErr extends CriticalErr {
    // can add more details here
}

/**
 * Error related to version mismatch. When we detect the version of a document
 * has been changed and our version might not be the latest. This can happen in case
 * users open the same document and make modifications in multiple place without
 * synchronization. This error can be thrown by regular version check
 * or maybe by specific error returned by the server which reject modification due to
 * version changed.
 */
export class DocumentVersionMismatchErr extends CriticalErr {
    // can add more details here
}

/**
 * Error thrown when the user provides an invalid input.
 * For example, the formula is malformed, or the user provides an invalid value
 * for a specific property of a specific object (e.g. invalid color)
 */
export class InvalidUserInputErr extends NonCriticalErr {
    // can add more details here
}
