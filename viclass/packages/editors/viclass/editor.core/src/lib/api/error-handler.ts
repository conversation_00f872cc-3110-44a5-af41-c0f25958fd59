import { DefaultEventEmitter } from '../default.event.source';
import { ViErr } from './error';
import { VEventData, VEventListener } from './events';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';

export interface ViErrEventData extends VEventData<any, any, ViErr> {}

export class ViErrEventEmitter extends DefaultEventEmitter<ViErrEventData> {}

export interface ViErrEventListener extends VEventListener<ViErrEventData> {
    subscribe(emitters: ViErrEventEmitter[]): void;
    unsubscribe(): void;
}

// A class to handle errors and emit them as events.
export class ErrorHandler {
    constructor(public readonly emitter = new ViErrEventEmitter()) {}

    // Handles an error event by emitting it and optionally propagating it.
    handle(e: ViErrEventData): ViErrEventData | null {
        this.emitter.emit(e);
        return e; // Return the error if further propagation is needed.
    }

    protected emit(e: ViErrEventData): void {
        this.emitter.emit(e);
    }
}

// Type alias for a function that returns an ErrorHandler instance.
export type ErrorHandlerFn = (ctx: any) => ErrorHandler;

// A decorator factory to handle errors using a list of error handlers.
export function ErrorHandlerDecorator(errorHandlersFn: (ErrorHandlerFn | ErrorHandler)[]) {
    // Internal function to handle errors using the provided handlers.
    const handleError = (ctx: any, errorHandlersFn: (ErrorHandlerFn | ErrorHandler)[], err: any): void => {
        // Normalize the error into a ViErrEventData object.
        const errEventData: ViErrEventData =
            err instanceof ViErr
                ? <ViErrEventData>{ state: err }
                : err?.state instanceof ViErr
                  ? err
                  : <ViErrEventData>{ state: new ViErr(err.message, err) };

        let result: ViErrEventData | null | undefined = errEventData;

        // Iterate through the error handlers and process the error.
        for (const handler of errorHandlersFn ?? []) {
            result = (handler instanceof ErrorHandler ? handler : handler(ctx)).handle(errEventData);
            if (!result) break; // Stop processing if the handler returns null.
        }

        // If the error is not fully handled, throw it.
        if (result) throw result.state;
    };

    return function (target: any, key: string, descriptor: PropertyDescriptor) {
        const method = descriptor.value; // The original method.
        descriptor.value = function (...args: any[]) {
            // Wrap the method to handle errors.
            try {
                const result = method.apply(this, args); // Call the original method.
                return result instanceof Promise
                    ? result.catch(err => handleError(this, errorHandlersFn, err)) // Handle async errors.
                    : result instanceof Observable
                      ? result.pipe(catchError(async err => handleError(this, errorHandlersFn, err))) // Handle Observable errors.
                      : result; // Return the result for sync cases.
            } catch (err) {
                handleError(this, errorHandlersFn, err); // Handle sync errors.
            }
        };

        return descriptor;
    };
}

// Abstract class for a default error handler listener.
export abstract class DefaultErrorHandlerListener implements ViErrEventListener {
    private emitters: ViErrEventEmitter[] = []; // List of subscribed emitters.

    // Subscribe to multiple emitters and register this listener.
    subscribe(emitters: ViErrEventEmitter[]): void {
        this.emitters = [...this.emitters, ...emitters];
        emitters.map(handler => handler.registerListener(this));
    }

    // Unsubscribe from all emitters and clear the list.
    unsubscribe(): void {
        this.emitters.forEach(handler => handler.unregisterListener(this));
        this.emitters = [];
    }

    // Abstract method to handle events, to be implemented by subclasses.
    abstract onEvent(data: ViErrEventData): ViErrEventData | Promise<ViErrEventData>;
}
