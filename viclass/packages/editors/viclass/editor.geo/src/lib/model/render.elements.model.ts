import { DocumentId } from '@viclass/editor.core';
import { GeoRenderer } from '../renderer';
import { DocRenderProp } from './gateway.models';
import { GeoObjectType } from './geo.models';
import { refElFromIdx } from './preview.util';
import { MovementPath } from './render.movement.path';

export const NOT_SET_VALUE = -1;
export type GeoStrokeStyle = 'Solid' | 'Dashed' | 'Dotted' | 'DashedDotted';
export type GeoRelType =
    | 'RenderVertex'
    | 'RenderPolygon'
    | 'RenderLine'
    | 'RenderLineSegment'
    | 'RenderVector'
    | 'RenderRay'
    | 'RenderCircleShape'
    | 'RenderEllipseShape'
    | 'RenderSectorShape'
    | 'RenderAngle'
    | 'RenderSector'
    | 'RenderEllipse'
    | 'RenderCircle';
export type LineType = 'Line' | 'Ray' | 'Segment' | 'Vector';
export type GeoRenderPropType =
    | 'Default'
    | 'Line'
    | 'LineSegment'
    | 'Point'
    | 'Sector'
    | 'Ellipse'
    | 'Circle'
    | 'Polygon'
    | 'PolygonEdge'
    | 'SectorShape'
    | 'CircleShape'
    | 'EllipseShape'
    | 'Angle';

export enum SettingPropertyType {
    color = 'color',
    lineColor = 'lineColor',
    pointColor = 'pointColor',
    strokeStyle = 'strokeStyle',
    lineWeight = 'lineWeight',
    hidden = 'hidden',
    opacity = 'opacity',
    showLabel = 'showLabel',
    labelType = 'labelType',
    label = 'label',
    swapLabelPosition = 'swapLabelPosition',
    spaceFromArcToCorner = 'spaceFromArcToCorner',
    showAngleTypes = 'showAngleTypes',
    angleArc = 'angleArc',
    enableEqualSegmentSign = 'enableEqualSegmentSign',
    equalSegmentSign = 'equalSegmentSign',
    showArcLabel = 'showArcLabel',
    arcLabelType = 'arcLabelType',
    arcLabelContent = 'arcLabelContent',
    isShowAngleSize = 'isShowAngleSize',
    pointLabelType = 'pointLabelType',
    pointLabelFreeContent = 'pointLabelFreeContent',
    showPointLabel = 'showPointLabel',
}

export enum SettingPropertyGroup {
    Default = 'Default',
    Line = 'Line',
    Shape = 'Shape',
    Angle = 'Angle',
    Point = 'Point',
}

export const settingPropertyConfig: {
    [key in SettingPropertyType]: {
        groups: Set<SettingPropertyGroup>;
        multipleGroup: boolean;
        multipleItem: boolean;
    };
} = {
    color: {
        groups: new Set([SettingPropertyGroup.Shape, SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    pointColor: {
        groups: new Set([SettingPropertyGroup.Point]),
        multipleGroup: true,
        multipleItem: true,
    },
    opacity: {
        groups: new Set([SettingPropertyGroup.Shape]),
        multipleGroup: true,
        multipleItem: true,
    },
    lineColor: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    strokeStyle: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    lineWeight: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    labelType: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: false,
        multipleItem: false,
    },
    showLabel: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    label: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: false,
        multipleItem: false,
    },
    swapLabelPosition: {
        groups: new Set([SettingPropertyGroup.Line]),
        multipleGroup: true,
        multipleItem: true,
    },
    enableEqualSegmentSign: {
        groups: new Set([SettingPropertyGroup.Line, SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    equalSegmentSign: {
        groups: new Set([SettingPropertyGroup.Line, SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    spaceFromArcToCorner: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    showAngleTypes: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    angleArc: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    showArcLabel: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    arcLabelType: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    arcLabelContent: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    isShowAngleSize: {
        groups: new Set([SettingPropertyGroup.Angle]),
        multipleGroup: true,
        multipleItem: true,
    },
    hidden: {
        groups: new Set([
            SettingPropertyGroup.Shape,
            SettingPropertyGroup.Line,
            SettingPropertyGroup.Point,
            SettingPropertyGroup.Angle,
        ]),
        multipleGroup: true,
        multipleItem: true,
    },
    pointLabelType: {
        groups: new Set([SettingPropertyGroup.Point]),
        multipleGroup: false,
        multipleItem: false,
    },
    pointLabelFreeContent: {
        groups: new Set([SettingPropertyGroup.Point]),
        multipleGroup: false,
        multipleItem: false,
    },
    showPointLabel: {
        groups: new Set([SettingPropertyGroup.Point]),
        multipleGroup: true,
        multipleItem: true,
    },
};

export enum DocRenderPropType {
    screenUnit = 'screenUnit',
    canvasWidth = 'canvasWidth',
    canvasHeight = 'canvasHeight',
    scale = 'scale',
    translation = 'translation',
    rotation = 'rotation',
    valid = 'valid',

    // background
    background = 'background',
    backgroundColor = 'backgroundColor',

    // shadow
    shadow = 'shadow',
    shadowStyle = 'shadowStyle',

    // grid
    axis = 'axis',
    grid = 'grid',
    detailGrid = 'detailGrid',

    // border
    border = 'border',
    borderStyle = 'borderStyle',
    borderColor = 'borderColor',

    // snap
    snapMode = 'snapMode',
    snapToExistingPoints = 'snapToExistingPoints',
    snapToGrid = 'snapToGrid',

    namingMode = 'namingMode',
}

export type SettingPropertyLabelType = 'size' | 'free';
export type SettingPropertyPointLabelType = 'name' | 'free';
export type SettingPropertyEqualSegmentSign = 'one' | 'two' | 'three';

export class GeoRenderDocState implements DocRenderProp {
    /**
     * Id of the geo doc that generates this render doc
     * When a render doc is created and the geodoc itself has not been persisted
     * then this id might be null.
     */
    docId: DocumentId;

    numDim: number = 2;

    screenUnit: number = 10;
    canvasWidth: number = 200.0;
    canvasHeight: number = 200.0;
    scale: number = 1.0;
    translation: number[] = [0.0, 0.0, 0.0];
    rotation: number[] = [0.0, 0.0, 0.0];
    valid: boolean;

    shadow: boolean;
    shadowStyle: string;

    // grid
    axis: boolean;
    grid: boolean;
    detailGrid: boolean;

    // border
    border: boolean;
    borderStyle: string;
    borderColor: string;

    // snap
    snapMode: boolean;
    snapToExistingPoints: boolean;
    snapToGrid: boolean;

    // naming mode
    namingMode: boolean;
}

export interface IGeoRenderProp {
    type: GeoRenderPropType;
    settingProperties: Set<SettingPropertyType>;
}

export class DefaultGeoRenderProp {
    color?: string;
    lineColor?: string;
    pointColor?: string;
    opacity?: number;
    lineWeight?: number;
    strokeStyle?: GeoStrokeStyle;
    spaceFromArcToCorner?: number;
}

export type GeoRenderProp = {
    type: GeoRenderPropType;
    group: SettingPropertyGroup;

    settingProperties: Set<SettingPropertyType>;

    /**
     * Whether an element should be shown. By default,
     * an element that is inferred will be hidden, and
     * an element that is created by user will be shown.
     */
    hidden: boolean;
};

// ---- RENDER PROP DEFINITION -----------------

export function _geoRenderProp(
    type?: GeoRenderPropType,
    group?: SettingPropertyGroup,
    settingProperties?: Set<SettingPropertyType>,
    hidden?: boolean
): GeoRenderProp {
    return {
        type: type ? type : 'Default',
        group: group ? group : SettingPropertyGroup.Default,
        settingProperties: settingProperties ? settingProperties : new Set<SettingPropertyType>([]),
        hidden: hidden !== undefined ? hidden : false,
    };
}

// -----------------------------------------------------------------

const _pointSettingProps = new Set<SettingPropertyType>([
    SettingPropertyType.pointColor,
    SettingPropertyType.showPointLabel,
    SettingPropertyType.pointLabelType,
    SettingPropertyType.pointLabelFreeContent,
    SettingPropertyType.hidden,
]);

export type PointGeoRenderProp = GeoRenderProp & {
    pointColor: string;
    showPointLabel: boolean;
    pointLabelType: SettingPropertyPointLabelType;
    pointLabelFreeContent: string;
};

export function _pointGeoRenderProp(): PointGeoRenderProp {
    return {
        ..._geoRenderProp('Point', SettingPropertyGroup.Point, _pointSettingProps, false),
        pointColor: '#00aeef',
        showPointLabel: true,
        pointLabelType: 'name',
        pointLabelFreeContent: '',
    };
}

export type LineGeoRenderProp = GeoRenderProp & {
    lineColor: string;
    lineWeight: number;
    strokeStyle: GeoStrokeStyle;
    showLabel: boolean;
    label: string;
    swapLabelPosition: boolean;
};

// -----------------------------------------------------------------

const _lineSettingPropArr = [
    SettingPropertyType.lineColor,
    SettingPropertyType.lineWeight,
    SettingPropertyType.strokeStyle,
    SettingPropertyType.showLabel,
    SettingPropertyType.label,
    SettingPropertyType.swapLabelPosition,
    SettingPropertyType.hidden,
];
const _lineSettingProps = new Set<SettingPropertyType>(_lineSettingPropArr);

export function _lineGeoRenderProp(): LineGeoRenderProp {
    return {
        ..._geoRenderProp('Line', SettingPropertyGroup.Line, _lineSettingProps, false),
        lineColor: '#00aeef',
        lineWeight: 1,
        strokeStyle: 'Solid',
        showLabel: false,
        label: '',
        swapLabelPosition: false,
    };
}

export type LineSegmentGeoRenderProp = GeoRenderProp & {
    lineColor: string;
    lineWeight: number;
    strokeStyle: GeoStrokeStyle;
    showLabel: boolean;
    label: string;
    labelType: SettingPropertyLabelType;
    equalSegmentSign: SettingPropertyEqualSegmentSign;
    enableEqualSegmentSign: boolean;
    swapLabelPosition: boolean;
};

// -----------------------------------------------------------------

const _lineSegmentSettingProps = new Set<SettingPropertyType>(
    _lineSettingPropArr.concat([
        SettingPropertyType.labelType,
        SettingPropertyType.enableEqualSegmentSign,
        SettingPropertyType.equalSegmentSign,
    ])
);

export function _lineSegmentGeoRenderProp(): LineSegmentGeoRenderProp {
    return {
        ..._geoRenderProp('LineSegment', SettingPropertyGroup.Line, _lineSegmentSettingProps, false),
        lineColor: '#00aeef',
        lineWeight: 1,
        strokeStyle: 'Solid',
        showLabel: false,
        label: '',
        labelType: 'size',
        equalSegmentSign: 'one',
        enableEqualSegmentSign: false,
        swapLabelPosition: false,
    };
}

// -----------------------------------------------------------------

const _sectorSettingProps = new Set<SettingPropertyType>([
    SettingPropertyType.lineColor,
    SettingPropertyType.lineWeight,
    SettingPropertyType.strokeStyle,
    SettingPropertyType.showArcLabel,
    SettingPropertyType.arcLabelType,
    SettingPropertyType.arcLabelContent,
    SettingPropertyType.hidden,
]);

export type SectorGeoRenderProp = GeoRenderProp & {
    lineColor: string;
    lineWeight: number;
    strokeStyle: GeoStrokeStyle;
    showArcLabel: boolean;
    arcLabelType: string;
    arcLabelContent: string;
};

export function _sectorGeoRenderProp(): SectorGeoRenderProp {
    return {
        ..._geoRenderProp('Sector', SettingPropertyGroup.Line, _sectorSettingProps, false),
        lineColor: '#00aeef',
        lineWeight: 1,
        strokeStyle: 'Solid',
        showArcLabel: false,
        arcLabelType: '',
        arcLabelContent: '',
    };
}

// -----------------------------------------------------------------

const _sectorShapeSettingProps = new Set<SettingPropertyType>([
    SettingPropertyType.color,
    SettingPropertyType.opacity,
    SettingPropertyType.showArcLabel,
    SettingPropertyType.arcLabelType,
    SettingPropertyType.arcLabelContent,
    SettingPropertyType.hidden,
]);

export type SectorShapeGeoRenderProp = GeoRenderProp & {
    color: string;
    opacity: number;
    showArcLabel: boolean;
    arcLabelType: string;
    arcLabelContent: string;
};

export function _sectorShapeGeoRenderProp(): SectorShapeGeoRenderProp {
    return {
        ..._geoRenderProp('SectorShape', SettingPropertyGroup.Shape, _sectorShapeSettingProps, false),
        color: '#00aeef',
        opacity: 5,
        showArcLabel: false,
        arcLabelType: '',
        arcLabelContent: '',
    };
}

// -----------------------------------------------------------------

const _ellipseSettingProps = new Set<SettingPropertyType>([
    SettingPropertyType.lineColor,
    SettingPropertyType.lineWeight,
    SettingPropertyType.strokeStyle,
    SettingPropertyType.label,
    SettingPropertyType.hidden,
]);

export type EllipseGeoRenderProp = GeoRenderProp & {
    lineColor: string;
    lineWeight: number;
    strokeStyle: GeoStrokeStyle;
    label: string;
};

export function _ellipseGeoRenderProp(): EllipseGeoRenderProp {
    return {
        ..._geoRenderProp('Ellipse', SettingPropertyGroup.Line, _ellipseSettingProps, false),
        lineColor: '#00aeef',
        lineWeight: 1,
        strokeStyle: 'Solid',
        label: '',
    };
}

// -----------------------------------------------------------------

const _circleSettingProps = new Set<SettingPropertyType>([
    SettingPropertyType.lineColor,
    SettingPropertyType.lineWeight,
    SettingPropertyType.strokeStyle,
    SettingPropertyType.label,
    SettingPropertyType.hidden,
]);

export type CircleGeoRenderProp = GeoRenderProp & {
    lineColor: string;
    lineWeight: number;
    strokeStyle: GeoStrokeStyle;
    label: string;
};

export function _circleGeoRenderProp(): CircleGeoRenderProp {
    return {
        ..._geoRenderProp('Circle', SettingPropertyGroup.Line, _circleSettingProps, false),
        lineColor: '#00aeef',
        lineWeight: 1,
        strokeStyle: 'Solid',
        label: '',
    };
}

// -----------------------------------------------------------------

const _polygonSettingProps = new Set<SettingPropertyType>([
    SettingPropertyType.color,
    SettingPropertyType.opacity,
    SettingPropertyType.hidden,
]);

export type PolygonGeoRenderProp = GeoRenderProp & {
    color: string;
    opacity: number;
};

export function _polygonGeoRenderProp(): PolygonGeoRenderProp {
    return {
        ..._geoRenderProp('Polygon', SettingPropertyGroup.Shape, _polygonSettingProps, false),
        color: '#00aeef',
        opacity: 5,
    };
}

// -----------------------------------------------------------------

const _circleShapeSettingProps = new Set<SettingPropertyType>([
    SettingPropertyType.color,
    SettingPropertyType.opacity,
    SettingPropertyType.strokeStyle,
    SettingPropertyType.lineWeight,
    SettingPropertyType.hidden,
]);

export type CircleShapeGeoRenderProp = GeoRenderProp & {
    color: string;
    opacity: number;
    strokeStyle: GeoStrokeStyle;
    lineWeight: number;
};

export function _circleShapeGeoRenderProp(): CircleShapeGeoRenderProp {
    return {
        ..._geoRenderProp('CircleShape', SettingPropertyGroup.Shape, _circleShapeSettingProps, false),
        color: '#00aeef',
        opacity: 5,
        strokeStyle: 'Solid',
        lineWeight: 1,
    };
}

// -----------------------------------------------------------------

const _ellipseShapeSettingProps = new Set<SettingPropertyType>([
    SettingPropertyType.color,
    SettingPropertyType.opacity,
    SettingPropertyType.strokeStyle,
    SettingPropertyType.lineWeight,
    SettingPropertyType.hidden,
]);

export type EllipseShapeGeoRenderProp = GeoRenderProp & {
    color: string;
    opacity: number;
    strokeStyle: GeoStrokeStyle;
    lineWeight: number;
};

export function _ellipseShapeGeoRenderProp(): EllipseShapeGeoRenderProp {
    return {
        ..._geoRenderProp('EllipseShape', SettingPropertyGroup.Shape, _ellipseShapeSettingProps, false),
        color: '#00aeef',
        opacity: 5,
        strokeStyle: 'Solid',
        lineWeight: 1,
    };
}

// -----------------------------------------------------------------

const _angleSettingProps = new Set<SettingPropertyType>([
    SettingPropertyType.color,
    SettingPropertyType.opacity,
    SettingPropertyType.spaceFromArcToCorner,
    SettingPropertyType.angleArc,
    SettingPropertyType.showAngleTypes,
    SettingPropertyType.enableEqualSegmentSign,
    SettingPropertyType.equalSegmentSign,
    SettingPropertyType.isShowAngleSize,
    SettingPropertyType.hidden,
]);

export type AngleGeoRenderProp = GeoRenderProp & {
    color: string;
    opacity: number;
    isShowAngleSize: boolean;
    showAngleTypes: string;
    angleArc: number;
    enableEqualSegmentSign: boolean;
    equalSegmentSign: SettingPropertyEqualSegmentSign;
    spaceFromArcToCorner: number;
};

export function _angleGeoRenderProp(): AngleGeoRenderProp {
    return {
        ..._geoRenderProp('Angle', SettingPropertyGroup.Angle, _angleSettingProps, false),
        color: '#00aeef',
        opacity: 5,
        isShowAngleSize: false,
        showAngleTypes: 'as2',
        angleArc: 1,
        enableEqualSegmentSign: false,
        equalSegmentSign: 'one',
        spaceFromArcToCorner: 25,
    };
}

// ----------- ELEMENT MODEL DEFINITIONS ----------------------

export abstract class GeoRenderElement {
    // type of render element
    abstract readonly type: GeoRelType;

    // type of element that this render element represent for
    abstract readonly elType: GeoObjectType;

    abstract renderProp?: GeoRenderProp;

    vertexRelIdxes?: number[];
    lineRelIdxes?: number[];
    arcRelIdx?: number;

    unselectable?: boolean;
    relIndex: number;
    name: string = '';

    /**
     * Whether an element is usable or not.
     */
    usable: boolean = true;

    /**
     * Whether an element is usable or not.
     */
    deleted?: boolean;

    /**
     * Whether an element is valid or not
     */
    valid: boolean = true;

    pInfo?: GeoPreviewInfo = undefined;
}

export type FillType = RenderSectorShape | RenderEllipseShape | RenderCircleShape | RenderPolygon;
export type StrokeType = RenderLine | RenderLineSegment | RenderSector | RenderCircle | RenderEllipse;

export class RenderVertex extends GeoRenderElement {
    constructor(public override renderProp?: PointGeoRenderProp) {
        super();
    }

    override readonly type = 'RenderVertex';
    override readonly elType = 'Point';
    coords: number[] = [];
    movementPath?: MovementPath = undefined; // null meaning unable to move
}

export class RenderLine extends GeoRenderElement {
    constructor(public override renderProp?: LineGeoRenderProp) {
        super();
    }

    override readonly type: GeoRelType = 'RenderLine';
    override readonly elType: GeoObjectType = 'LineVi';

    override vertexRelIdxes: number[] = [];

    startPointIdx: number = NOT_SET_VALUE;
    endPointIdx: number = NOT_SET_VALUE;
    vector: number[] = [];

    length: number = NOT_SET_VALUE;

    override pInfo?: PreviewLine = undefined;

    /**
     * Get the coordinate of the end point or start point. If can find the element somewhere,
     * use the element to get idx, otherwise, use the preview coord index.
     * @param point
     * @param renderer
     * @returns
     */
    coord(point: 'start' | 'end', renderer: GeoRenderer): number[] | undefined {
        const idx = point == 'start' ? this.startPointIdx : this.endPointIdx;

        if (idx != NOT_SET_VALUE) {
            const refEl = refElFromIdx(idx, this, renderer);

            if (refEl && refEl instanceof RenderVertex) {
                return refEl.coords;
            }
        }

        if (point == 'start' && this.pInfo?.sVCoords) return this.pInfo.sVCoords;
        else if (point == 'end' && this.pInfo?.eVCoords) return this.pInfo.eVCoords;

        return undefined;
    }
}

export class RenderLineSegment extends RenderLine {
    constructor(public override renderProp?: LineSegmentGeoRenderProp) {
        super(renderProp);
    }

    override readonly type = 'RenderLineSegment';
    override readonly elType = 'LineSegment';
}

export class RenderRay extends RenderLine {
    override readonly type = 'RenderRay';
    override readonly elType = 'Ray';
}

export class RenderVector extends RenderLine {
    override readonly type = 'RenderVector';
    override readonly elType = 'VectorVi';

    point1?: number[];
    point2?: number[];
}

export class RenderEllipse extends GeoRenderElement {
    constructor(public override renderProp?: EllipseGeoRenderProp) {
        super();
    }

    override readonly type = 'RenderEllipse';
    override readonly elType = 'Ellipse';

    override vertexRelIdxes: number[] = [];

    f1Idx: number = NOT_SET_VALUE;
    f2Idx: number = NOT_SET_VALUE;
    a: number = NOT_SET_VALUE;
    b: number = NOT_SET_VALUE;
    rotate: number = NOT_SET_VALUE;

    length: number = NOT_SET_VALUE;

    override pInfo?: PreviewEllipseShape = undefined;

    coord(point: 'f1' | 'f2', renderer: GeoRenderer): number[] {
        const idx = point == 'f1' ? this.f1Idx : this.f2Idx;

        if (idx != NOT_SET_VALUE) {
            const refEl = refElFromIdx(idx, this, renderer);

            if (refEl && refEl instanceof RenderVertex) {
                return refEl.coords;
            }
        }

        if (point == 'f1' && this.pInfo?.f1) return this.pInfo.f1;
        else if (point == 'f2' && this.pInfo?.f2) return this.pInfo.f2;

        return undefined;
    }
}

export class RenderEllipseShape extends GeoRenderElement {
    constructor(public override renderProp?: EllipseShapeGeoRenderProp) {
        super();
    }

    override vertexRelIdxes: number[] = [];
    override arcRelIdx: number = NOT_SET_VALUE;

    override readonly type = 'RenderEllipseShape';
    override readonly elType = 'Ellipse';
    f1Idx: number = NOT_SET_VALUE;
    f2Idx: number = NOT_SET_VALUE;
    a: number = NOT_SET_VALUE;
    b: number = NOT_SET_VALUE;
    rotate: number = NOT_SET_VALUE;

    area: number = NOT_SET_VALUE;
    perimeter: number = NOT_SET_VALUE;

    override pInfo?: PreviewEllipseShape = undefined;

    coord(point: 'f1' | 'f2', renderer: GeoRenderer): number[] {
        const idx = point == 'f1' ? this.f1Idx : this.f2Idx;

        if (idx != NOT_SET_VALUE) {
            const refEl = refElFromIdx(idx, this, renderer);

            if (refEl && refEl instanceof RenderVertex) {
                return refEl.coords;
            }
        }

        if (point == 'f1' && this.pInfo?.f1) return this.pInfo.f1;
        else if (point == 'f2' && this.pInfo?.f2) return this.pInfo.f2;

        return undefined;
    }
}

export class RenderSector extends GeoRenderElement {
    constructor(public override renderProp?: SectorGeoRenderProp) {
        super();
    }

    override readonly type = 'RenderSector';
    override readonly elType = 'CircularSector';

    length: number = 0;
    override vertexRelIdxes: number[] = [];

    centerPointIdx: number = NOT_SET_VALUE;
    startPointIdx: number = NOT_SET_VALUE;
    endPointIdx: number = NOT_SET_VALUE;
    radius: number = NOT_SET_VALUE;

    override pInfo?: PreviewSector = undefined;

    coord(point: 'center' | 'start' | 'end', renderer: GeoRenderer): number[] {
        const idx = point == 'center' ? this.centerPointIdx : point == 'start' ? this.startPointIdx : this.endPointIdx;

        if (idx != NOT_SET_VALUE) {
            const refEl = refElFromIdx(idx, this, renderer);

            if (refEl && refEl instanceof RenderVertex) {
                return refEl.coords;
            }
        }

        if (point == 'center' && this.pInfo?.cCoords) return this.pInfo.cCoords;
        else if (point == 'start' && this.pInfo?.sCoords) return this.pInfo.sCoords;
        else if (point == 'end' && this.pInfo?.eCoords) return this.pInfo.eCoords;

        return undefined;
    }
}

export class RenderSectorShape extends GeoRenderElement {
    constructor(public override renderProp?: SectorShapeGeoRenderProp) {
        super();
    }

    override readonly type = 'RenderSectorShape';
    override readonly elType = 'CircularSector';

    override vertexRelIdxes: number[] = [];
    override lineRelIdxes: number[] = [];
    override arcRelIdx: number = NOT_SET_VALUE;

    centerPointIdx: number = NOT_SET_VALUE;
    startPointIdx: number = NOT_SET_VALUE;
    endPointIdx: number = NOT_SET_VALUE;
    radius: number = NOT_SET_VALUE;

    area: number = NOT_SET_VALUE;

    length: number = NOT_SET_VALUE;

    override pInfo?: PreviewSector = undefined;

    coord(point: 'center' | 'start' | 'end', renderer: GeoRenderer): number[] {
        const idx = point == 'center' ? this.centerPointIdx : point == 'start' ? this.startPointIdx : this.endPointIdx;

        if (idx != NOT_SET_VALUE) {
            const refEl = refElFromIdx(idx, this, renderer);

            if (refEl && refEl instanceof RenderVertex) {
                return refEl.coords;
            }
        }

        if (point == 'center' && this.pInfo?.cCoords) return this.pInfo.cCoords;
        else if (point == 'start' && this.pInfo?.sCoords) return this.pInfo.sCoords;
        else if (point == 'end' && this.pInfo?.eCoords) return this.pInfo.eCoords;

        return undefined;
    }
}

export class RenderCircle extends GeoRenderElement {
    constructor(public override renderProp?: CircleGeoRenderProp) {
        super();
    }

    override readonly type = 'RenderCircle';
    override readonly elType = 'Circle';

    override vertexRelIdxes: number[] = [];

    centerPointIdx: number = NOT_SET_VALUE;
    radius: number = NOT_SET_VALUE;

    length: number = NOT_SET_VALUE;

    override pInfo?: PreviewCircleShape = undefined;

    coord(point: 'center', renderer: GeoRenderer): number[] {
        const idx = point == 'center' ? this.centerPointIdx : NOT_SET_VALUE;

        if (idx != NOT_SET_VALUE) {
            const refEl = refElFromIdx(idx, this, renderer);

            if (refEl && refEl instanceof RenderVertex) {
                return refEl.coords;
            }
        }

        if (point == 'center' && this.pInfo?.cCoords) return this.pInfo.cCoords;

        return undefined;
    }
}

export class RenderCircleShape extends GeoRenderElement {
    constructor(public override renderProp?: CircleShapeGeoRenderProp) {
        super();
    }

    override readonly type = 'RenderCircleShape';
    override readonly elType = 'Circle';

    override arcRelIdx: number = NOT_SET_VALUE;
    override vertexRelIdxes: number[] = [];
    centerPointIdx: number = NOT_SET_VALUE; // index of the vertex that is the center

    radius: number = NOT_SET_VALUE;
    area: number = NOT_SET_VALUE;
    perimeter: number = NOT_SET_VALUE;

    override pInfo?: PreviewCircleShape = undefined;

    coord(point: 'center', renderer: GeoRenderer): number[] {
        const idx = point == 'center' ? this.centerPointIdx : NOT_SET_VALUE;

        if (idx != NOT_SET_VALUE) {
            const refEl = refElFromIdx(idx, this, renderer);

            if (refEl && refEl instanceof RenderVertex) {
                return refEl.coords;
            }
        }

        if (point == 'center' && this.pInfo?.cCoords) return this.pInfo.cCoords;

        return undefined;
    }
}

export class RenderPolygon extends GeoRenderElement {
    constructor(public override renderProp?: PolygonGeoRenderProp) {
        super();
    }

    override readonly type = 'RenderPolygon';
    override readonly elType = 'Polygon';

    faces: number[] = []; // indexes of points
    override vertexRelIdxes: number[] = [];
    override lineRelIdxes: number[] = [];

    area: number = NOT_SET_VALUE;
    perimeter: number = NOT_SET_VALUE;

    override pInfo?: PreviewPolygon = undefined;

    /**
     * Get the coordinate of the end point or start point. If can find the element somewhere,
     * use the element to get idx, otherwise, use the preview coord index.
     * @param point the index of the point whose coords needs to be retrieved
     * @param renderer
     * @returns
     */
    coord(point: number, renderer: GeoRenderer): number[] | undefined {
        const idx = this.faces[point];

        if (idx != NOT_SET_VALUE) {
            const refEl = refElFromIdx(idx, this, renderer);

            if (refEl && refEl instanceof RenderVertex) {
                return refEl.coords;
            }
        }
        if (this.pInfo?.verts?.has(point)) return this.pInfo.verts.get(point);
        return undefined;
    }
}

export class RenderAngle extends GeoRenderElement {
    constructor(public override renderProp?: AngleGeoRenderProp) {
        super();
    }

    override readonly type = 'RenderAngle';
    override readonly elType = 'Angle';

    anglePointIdx: number = NOT_SET_VALUE;
    vectorStart: number[] = [0.0, 0.0, 0.0];
    vectorEnd: number[] = [0.0, 0.0, 0.0];

    override vertexRelIdxes: number[] = [];
    override lineRelIdxes: number[] = [];

    // render fields calculated on the client side
    startAngleInRad?: number;
    endAngleInRad?: number;
    angle?: number;
    centerAngle?: number;
    labelAngle?: number;

    // calculated from the server side
    degree: number = NOT_SET_VALUE;

    override pInfo?: PreviewAngle = undefined;

    coord(point = 'root', renderer: GeoRenderer): number[] {
        const idx = point == 'root' ? this.anglePointIdx : NOT_SET_VALUE;

        if (idx != NOT_SET_VALUE) {
            const refEl = refElFromIdx(idx, this, renderer);

            if (refEl && refEl instanceof RenderVertex) {
                return refEl.coords;
            }
        }

        if (point == 'root' && this.pInfo?.cCoords) return this.pInfo.cCoords;

        return undefined;
    }
}

// ------------- GEO PREVIEW ELEMENT MODEL ---------------------

/**
 * Information to render the preview.
 *
 * The preview to be render, basically is a RenderElement.
 *
 * When creating preview, the composing elements are created incrementally. For example
 * - When create a Line Preview, its vertices are created first during the select points step
 * - When create angle, its vertices are created first during the select point step, or calculated from
 * intersection of two lines.
 *
 * Because of the incremental nature of previewing, it is necessary for the preview receipient to be
 * able to construct the whole preview if needed, including all the current previewing elements' constituents
 *
 * To do this we follow this rule when creating preview:
 * - The preview object represent a Render object, so it include information of the render object.
 * - If the render object refer to other render / preview objects there will be a few cases:
 *
 *   + If the referred object is a real render object no other information is needed, because it exist
 *   when loading the document from server.
 *
 *   + If the referred object is a preview object, it has to be included with the main preview object
 *   This allow any user peer joining classroom to recreated preview objects even though he might have missed
 *   the the previous incremental step (e.g. create point in lines)
 *
 *   + If the referred object is specified as NOT USED e.g. by utilizing a specific id, we include the necessary info
 *   in the preview to calculate the previewobject. For example, when rendering
 *   the preview line when constructing isoceles triangle, we want to preview the mid-section line WITHOUT showing
 *   its go through points, we can set the start point index as -INFINITY to signify it is not used, and send the
 *   coordinates of the start point.
 *
 * When rendering the preview object, the renderer should create any missing specified preview element and add it to
 * the preview collection.
 *
 */
export type GeoPreviewInfo = {
    /**
     * Reference preview elements, these are the preview elements
     * created in the incremental steps leading to this preview element
     * and whose indexes are refered to by this preview element. We include
     * them so if a peer missed those incremental steps, they still can render
     * the preview element.
     */
    refPEl: GeoRenderElement[];
};

export type PreviewLine = GeoPreviewInfo & {
    // in cases we don't want to render the line with any of its vertices.
    sVCoords?: number[];
    eVCoords?: number[];
};

export type PreviewCircleShape = GeoPreviewInfo & {
    cCoords?: number[];
};

export type PreviewEllipseShape = GeoPreviewInfo & {
    f1?: number[];
    f2?: number[];
};

export type PreviewSector = GeoPreviewInfo & {
    cCoords?: number[];
    sCoords?: number[];
    eCoords?: number[];
};

export type PreviewPolygon = GeoPreviewInfo & {
    // coordinates of the constituent vertices, we use map, because if
    // the coordinate is already in the refPEL list or is an existing element
    //  its coordinates is not included in here
    verts?: Map<number, number[]>;
};

export type PreviewAngle = GeoPreviewInfo & {
    cCoords?: number[];
};

export type PreviewRay = GeoPreviewInfo & {
    cCoords?: number[];
};
