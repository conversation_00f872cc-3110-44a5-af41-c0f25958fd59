import { point, vector, Vector } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    RenderAngle,
    RenderLine,
    RenderLineSegment,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pAngle, pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { repeat, then, ThenSelector, vert, vertex, VertexSelector } from '../selectors';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildAngleConstructionFromLines,
    buildLineSegmentConstruction,
    buildPointConstruction,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isRenderVertex,
    pickPointName,
    requestElementNames,
} from './tool.utils';

/**
 * Refactored: CreateAngleByThreePointsTool
 * Creates angles from three points with proper counter-clockwise arc calculation.
 */
export class CreateAngleByThreePointsTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateAngleByThreePointsTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    renderAngle: RenderAngle | undefined;
    basePoints: RenderVertex[] = [];
    lastVertex: VertexSelector;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.renderAngle = undefined;
        this.basePoints = [];
        super.resetState();
    }

    get first3Points() {
        const first3 = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: (el: RenderVertex) => !this.basePoints.includes(el),
        });
        return repeat(first3, {
            count: 3,
            onPartialSelection: newSel => {
                const v = isRenderVertex(newSel) ? newSel : vert(newSel);
                this.basePoints.push(v);
                return true;
            },
        });
    }

    private createSelLogic() {
        this.lastVertex = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: (el: RenderVertex) => !this.basePoints.includes(el),
        });
        this.selLogic = then([this.first3Points, this.lastVertex], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [firstThree, directionPoint] = selector.selected;
                const points = [firstThree[0], firstThree[1], firstThree[2]] as RenderVertex[];
                this.performConstructionFromPoints(points, directionPoint as RenderVertex, doc);
            },
        });
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        if (!selected) {
            this.pQ.flush(ctrl);
            return;
        }

        if (selected && selected.length) {
            const basePoints = (selected[0] as RenderVertex[]).map(s => (isRenderVertex(s) ? s : vert(s)));

            // Preview line segments as user selects points
            if (basePoints.length >= 2) {
                const [p1, p2] = basePoints.slice(0, 2);
                this.pQ.add(pLine(ctrl, -21, RenderLineSegment, p1, p2));
            }
            if (basePoints.length === 3) {
                const [p2, p3] = basePoints.slice(1, 3);
                this.pQ.add(pLine(ctrl, -22, RenderLineSegment, p2, p3));

                // Preview angle when direction point is selected
                if (selected.length === 2) {
                    this.previewAngleFromThreePoints(basePoints, selected[1] as RenderVertex, ctrl);
                }
            }
        }

        this.pQ.flush(ctrl);
    }

    /**
     * Preview angle from three points with proper vector calculation
     */
    private previewAngleFromThreePoints(points: RenderVertex[], directionPoint: RenderVertex, ctrl: GeoDocCtrl) {
        const [p1, p2, p3] = points;

        // Convert to flatten-js points
        const fp1 = point(p1.coords[0], p1.coords[1]);
        const fp2 = point(p2.coords[0], p2.coords[1]); // vertex of angle
        const fp3 = point(p3.coords[0], p3.coords[1]);
        const fDirPt = point(directionPoint.coords[0], directionPoint.coords[1]);

        // Calculate vectors from vertex to other points
        const vec1 = vector(fp2, fp1);
        const vec3 = vector(fp2, fp3);
        const dirVec = vector(fp2, fDirPt);

        // Use cross product to determine which side of vec1->vec3 the direction point is on
        const cross1Dir = vec1.x * dirVec.y - vec1.y * dirVec.x; // cross product vec1 x dirVec
        const cross3Dir = vec3.x * dirVec.y - vec3.y * dirVec.x; // cross product vec3 x dirVec
        const crossVecs = vec1.x * vec3.y - vec1.y * vec3.x; // cross product vec1 x vec3

        let startVector: Vector, endVector: Vector;

        // Determine if direction point is in the angle between vec1 and vec3
        // If crossVecs > 0: vec3 is counter-clockwise from vec1
        // If crossVecs < 0: vec3 is clockwise from vec1

        if (crossVecs >= 0) {
            // vec3 is counter-clockwise from vec1
            // Check if dirVec is between vec1 and vec3 (in counter-clockwise direction)
            if (cross1Dir >= 0 && cross3Dir <= 0) {
                // Direction point is in the smaller angle, draw from vec1 to vec3
                startVector = vec1;
                endVector = vec3;
            } else {
                // Direction point is in the larger angle, draw from vec3 to vec1
                startVector = vec3;
                endVector = vec1;
            }
        } else {
            // vec3 is clockwise from vec1
            // Check if dirVec is between vec3 and vec1 (in counter-clockwise direction)
            if (cross3Dir >= 0 && cross1Dir <= 0) {
                // Direction point is in the smaller angle, draw from vec3 to vec1
                startVector = vec3;
                endVector = vec1;
            } else {
                // Direction point is in the larger angle, draw from vec1 to vec3
                startVector = vec1;
                endVector = vec3;
            }
        }

        // Create preview
        this.pQ.add(pAngle(ctrl, -24, fp2, startVector, endVector));
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstructionFromPoints(
        points: RenderVertex[],
        directionPoint: RenderVertex,
        ctrl: GeoDocCtrl
    ) {
        try {
            const pts = points.map(p => (isRenderVertex(p) ? p : vert(p)));
            const dirPt = isRenderVertex(directionPoint) ? directionPoint : vert(directionPoint);

            // Request names for points
            const inputPointNames = (
                await requestElementNames(ctrl, this.toolbar.getTool('NamingElementTool') as NamingElementTool, [
                    {
                        objName: 'Tên các điểm của góc',
                        originElement: pts,
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];
            if (!inputPointNames.length) return;

            // Create point constructions for new points
            const constructionPoints: GeoElConstructionRequest[] = pts
                .map((p, i) => {
                    if (p.relIndex < 0) {
                        p.name = inputPointNames[i];
                        return buildPointConstruction(p.name, { x: p.coords[0], y: p.coords[1] });
                    }
                    return null;
                })
                .filter(Boolean);

            const [p1, p2, p3] = pts;

            // Find or create lines between points
            const line1 = this.findOrCreateLine(ctrl, p1, p2, -21);
            const line2 = this.findOrCreateLine(ctrl, p2, p3, -22);

            // Set line names if they are new
            if (line1.relIndex < 0) {
                const startPt = p1.name < p2.name ? p1 : p2;
                const endPt = p1.name < p2.name ? p2 : p1;
                line1.name = `${startPt.name}${endPt.name}`;
            }

            if (line2.relIndex < 0) {
                const startPt = p2.name < p3.name ? p2 : p3;
                const endPt = p2.name < p3.name ? p3 : p2;
                line2.name = `${startPt.name}${endPt.name}`;
            }

            // Calculate angle geometry with proper direction using named lines
            const angleResult = this.calculateAngleFromPoints(pts, dirPt, line1, line2);
            if (!angleResult) return;

            // Create line constructions for new lines
            const constructionLines: GeoElConstructionRequest[] = [line1, line2]
                .map(l => (l.relIndex < 0 ? buildLineSegmentConstruction(l.name) : undefined))
                .filter(Boolean);

            // Create angle construction
            const angleName: string = pts.map(p => p.name).join('');
            const constructionAngle = buildAngleConstructionFromLines(
                angleResult.startLine.name,
                angleResult.startLine.elType,
                angleResult.endLine.name,
                angleResult.endLine.elType,
                angleName,
                angleResult.startDirection,
                angleResult.endDirection
            );

            await ctrl.editor.awarenessFeature.useAwareness(
                ctrl.viewport.id,
                'Đang tạo góc',
                buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
                async () => {
                    const constructResponse = await constructExec(() =>
                        this.editor.geoGateway.construct(ctrl.state.globalId, [
                            ...constructionPoints.map(c => <ConstructionRequest>{ construction: c }),
                            ...constructionLines.map(c => <ConstructionRequest>{ construction: c }),
                            { construction: constructionAngle },
                        ])
                    );
                    await syncRenderCommands(constructResponse.render, ctrl);
                    await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                }
            );
            this.resetState();
        } catch (err: unknown) {
            this.resetState();
            console.error(err);
            throw err;
        }
    }

    /**
     * Find existing line or create preview line between two points
     */
    private findOrCreateLine(ctrl: GeoDocCtrl, p1: RenderVertex, p2: RenderVertex, previewId: number): RenderLine {
        // Look for existing line between these points
        if (p1.relIndex >= 0 && p2.relIndex >= 0) {
            const existingLine = ctrl.rendererCtrl
                .lineElements(false)
                .find(
                    (l: RenderLine) =>
                        [p1.relIndex, p2.relIndex].includes(l.startPointIdx) &&
                        [p1.relIndex, p2.relIndex].includes(l.endPointIdx)
                ) as RenderLine;

            if (existingLine) return existingLine;
        }

        // Create preview line
        return pLine(ctrl, previewId, RenderLineSegment, p1.name < p2.name ? p1 : p2, p1.name < p2.name ? p2 : p1);
    }

    /**
     * Calculate angle direction information from three points
     */
    private calculateAngleFromPoints(pts: RenderVertex[], dirPt: RenderVertex, line1: RenderLine, line2: RenderLine) {
        const [p1, p2, p3] = pts;

        // Convert to flatten-js points
        const fp1 = point(p1.coords[0], p1.coords[1]);
        const fp2 = point(p2.coords[0], p2.coords[1]); // vertex of angle
        const fp3 = point(p3.coords[0], p3.coords[1]);
        const fDirPt = point(dirPt.coords[0], dirPt.coords[1]);

        // Calculate vectors from vertex to other points
        const vec1 = vector(fp2, fp1);
        const vec3 = vector(fp2, fp3);
        const dirVec = vector(fp2, fDirPt);

        // Use cross product to determine which side of vec1->vec3 the direction point is on
        const cross1Dir = vec1.x * dirVec.y - vec1.y * dirVec.x; // cross product vec1 x dirVec
        const cross3Dir = vec3.x * dirVec.y - vec3.y * dirVec.x; // cross product vec3 x dirVec
        const crossVecs = vec1.x * vec3.y - vec1.y * vec3.x; // cross product vec1 x vec3

        let startVector: Vector, endVector: Vector, startLine: RenderLine, endLine: RenderLine;

        // Determine if direction point is in the angle between vec1 and vec3
        if (crossVecs >= 0) {
            // vec3 is counter-clockwise from vec1
            if (cross1Dir >= 0 && cross3Dir <= 0) {
                // Direction point is in the smaller angle, draw from vec1 to vec3
                startVector = vec1;
                endVector = vec3;
                startLine = line1; // p1-p2 line
                endLine = line2; // p2-p3 line
            } else {
                // Direction point is in the larger angle, draw from vec3 to vec1
                startVector = vec3;
                endVector = vec1;
                startLine = line2; // p2-p3 line
                endLine = line1; // p1-p2 line
            }
        } else {
            // vec3 is clockwise from vec1
            if (cross3Dir >= 0 && cross1Dir <= 0) {
                // Direction point is in the smaller angle, draw from vec3 to vec1
                startVector = vec3;
                endVector = vec1;
                startLine = line2; // p2-p3 line
                endLine = line1; // p1-p2 line
            } else {
                // Direction point is in the larger angle, draw from vec1 to vec3
                startVector = vec1;
                endVector = vec3;
                startLine = line1; // p1-p2 line
                endLine = line2; // p2-p3 line
            }
        }

        // Calculate direction values: 1 if same direction, -1 if opposite
        // We need to compare the startVector/endVector with the actual direction of the lines

        // For each line, determine its actual direction vector
        let startLineDirection: Vector;
        let endLineDirection: Vector;

        // Get direction of startLine
        if (startLine === line1) {
            // line1 connects p1 and p2
            if (line1.relIndex < 0) {
                // New line - use naming convention
                startLineDirection = p1.name < p2.name ? vector(fp1, fp2) : vector(fp2, fp1);
            } else {
                // Existing line - use actual start/end points
                startLineDirection = line1.startPointIdx === p1.relIndex ? vector(fp1, fp2) : vector(fp2, fp1);
            }
        } else {
            // startLine is line2 (p2-p3)
            if (line2.relIndex < 0) {
                // New line - use naming convention
                startLineDirection = p2.name < p3.name ? vector(fp2, fp3) : vector(fp3, fp2);
            } else {
                // Existing line - use actual start/end points
                startLineDirection = line2.startPointIdx === p2.relIndex ? vector(fp2, fp3) : vector(fp3, fp2);
            }
        }

        // Get direction of endLine
        if (endLine === line1) {
            // endLine is line1 (p1-p2)
            if (line1.relIndex < 0) {
                // New line - use naming convention
                endLineDirection = p1.name < p2.name ? vector(fp1, fp2) : vector(fp2, fp1);
            } else {
                // Existing line - use actual start/end points
                endLineDirection = line1.startPointIdx === p1.relIndex ? vector(fp1, fp2) : vector(fp2, fp1);
            }
        } else {
            // endLine is line2 (p2-p3)
            if (line2.relIndex < 0) {
                // New line - use naming convention
                endLineDirection = p2.name < p3.name ? vector(fp2, fp3) : vector(fp3, fp2);
            } else {
                // Existing line - use actual start/end points
                endLineDirection = line2.startPointIdx === p2.relIndex ? vector(fp2, fp3) : vector(fp3, fp2);
            }
        }

        // Calculate directions: dot product > 0 means same direction, < 0 means opposite
        const startDirection = startLineDirection.dot(startVector) > 0 ? 1 : -1;
        const endDirection = endLineDirection.dot(endVector) > 0 ? 1 : -1;

        return {
            startLine,
            endLine,
            startDirection,
            endDirection,
        };
    }
}
