import { point, vector } from '@flatten-js/core';
import { UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderLineSegment, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue, pSector, pSectorShape } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import {
    circleTransform,
    nPoints,
    or,
    SelectedVertex,
    then,
    ThenSelector,
    vert,
    vertex,
    vertexOnStroke,
} from '../selectors';
import { getFocusDocCtrl } from '../tools/tool.utils';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { assignNames, handleIfPointerNotInError, pickShapeName, remoteConstruct } from './tool.utils';

export class CreateSectorTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateSectorTool';

    pQ = new PreviewQueue();
    declare selLogic?: ThenSelector;
    previewSector: any = null; // Store preview sector for reuse

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        this.selLogic.reset();
        this.previewSector = null; // Reset preview sector
        super.resetState();
    }

    protected createSelLogic() {
        const first2Points = nPoints(this.pQ, this.pointerHandler.cursor, {
            count: 2,
            onPartialSelection: (newSel: SelectedVertex, curSel, selector, doc) => {
                return true;
            },
        });

        const endPointSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: el => circleTransform(this.selLogic.selected[0] as SelectedVertex[], el, 0),
                }),
                vertexOnStroke({
                    selectableStrokeTypes: [
                        'RenderCircle',
                        'RenderLine',
                        'RenderLineSegment',
                        'RenderVector',
                        'RenderRay',
                        'RenderSector',
                    ],
                }),
            ],
            {
                flatten: true,
            }
        );

        this.selLogic = then([first2Points, endPointSelector], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl));

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (!selected || selected.length < 2) {
            this.pQ.flush(ctrl);
            return;
        }

        const first2Points = selected[0] as SelectedVertex[];
        const endSelection = selected[1] as SelectedVertex;

        if (!first2Points || first2Points.length < 2) {
            this.pQ.flush(ctrl);
            return;
        }

        const centerPoint = vert(first2Points[0]);
        const startPoint = vert(first2Points[1]);
        const endPoint = vert(endSelection);

        this.pQ.add(pLine(ctrl, -24, RenderLineSegment, startPoint, centerPoint));
        this.pQ.add(pLine(ctrl, -23, RenderLineSegment, centerPoint, endPoint));

        // Store sector for reuse in performConstruction
        this.previewSector = pSector(ctrl, -20, startPoint, centerPoint, endPoint);
        this.pQ.add(pSectorShape(ctrl, -21, startPoint, centerPoint, endPoint, this.previewSector));

        this.pQ.flush(ctrl);
    }

    async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = selector.selected;
        if (!selected || selected.length < 2) {
            this.resetState();
            return;
        }

        const first2Points = selected[0] as SelectedVertex[];
        const endSelection = selected[1] as SelectedVertex;

        if (!first2Points || first2Points.length < 2) {
            this.resetState();
            return;
        }

        const centerPoint = vert(first2Points[0]);
        const startPoint = vert(first2Points[1]);
        const endPoint = vert(endSelection);

        try {
            const hasStrokeDependency = Array.isArray(endSelection) && endSelection.length === 2;
            const hasName = endPoint.name && endPoint.name.length > 0;
            const isEndPointVirtual = !hasName && !hasStrokeDependency;

            if (isEndPointVirtual) {
                await this.handleAngleBasedApproach(
                    ctrl,
                    selected as [SelectedVertex[], SelectedVertex],
                    centerPoint,
                    startPoint,
                    endPoint
                );
            } else {
                await this.handleThreePointApproach(
                    ctrl,
                    selected as [SelectedVertex[], SelectedVertex],
                    centerPoint,
                    startPoint,
                    endPoint
                );
            }
        } finally {
            this.resetState();
        }
    }

    private async handleAngleBasedApproach(
        ctrl: GeoDocCtrl,
        selected: [SelectedVertex[], SelectedVertex],
        centerPoint: RenderVertex,
        startPoint: RenderVertex,
        endPoint: RenderVertex
    ) {
        const { pcs } = await assignNames(
            ctrl,
            [...selected[0], endPoint], // Include the actual endpoint
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Điểm cung tròn',
            'cung tròn',
            this.previewSector // Use the preview sector from doTrySelection
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        // Get the sector name from the preview sector
        const sectorName = this.previewSector.name || pickShapeName(ctrl, []);

        const pC = point(centerPoint.coords[0], centerPoint.coords[1]);
        const pS = point(startPoint.coords[0], startPoint.coords[1]);
        const pE = point(endPoint.coords[0], endPoint.coords[1]);

        const vecStart = vector(pC, pS);
        const vecEnd = vector(pC, pE);
        const angle = vecStart.angleTo(vecEnd);
        const sectorConstruction = new GeoElConstructionRequest(
            'CircularSector/CircularSectorEC',
            'CircularSector',
            'WithCenterAndStartPointAngleRadian'
        );
        sectorConstruction.name = sectorName; // Set sector name
        sectorConstruction.paramSpecs = [
            this.createParamSpec(0, 'aPoint', 'tpl-CenterCircle', centerPoint.name),
            this.createParamSpec(1, 'aPoint', 'tpl-Point', startPoint.name),
            this.createParamSpec(2, 'aValue', 'tpl-AngleRadian', angle),
            this.createParamSpec(3, 'aName', 'tpl-EndPoint', endPoint.name), // Add endpoint name
        ];

        // For angle-based approach, endPoint is virtual and will be created by backend
        // So only include constructions for center and start points
        const pointConstructions = pcs.filter(pc => pc.name === centerPoint.name || pc.name === startPoint.name);

        await remoteConstruct(ctrl, sectorConstruction, pointConstructions, this.editor.geoGateway, 'cung tròn');
    }

    private async handleThreePointApproach(
        ctrl: GeoDocCtrl,
        selected: [SelectedVertex[], SelectedVertex],
        centerPoint: RenderVertex,
        startPoint: RenderVertex,
        endPoint: RenderVertex
    ) {
        const allPoints = [...selected[0], selected[1]];

        const { pcs } = await assignNames(
            ctrl,
            allPoints,
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Điểm cung tròn',
            'cung tròn',
            this.previewSector // Use the preview sector from doTrySelection
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        // Get the sector name from the preview sector
        const sectorName = this.previewSector.name || pickShapeName(ctrl, []);

        const sectorConstruction = new GeoElConstructionRequest(
            'CircularSector/CircularSectorEC',
            'CircularSector',
            'ThreePoint'
        );
        sectorConstruction.name = sectorName; // Set sector name
        sectorConstruction.paramSpecs = [
            this.createParamSpec(0, 'aPoint', 'tpl-CenterPoint', centerPoint.name),
            this.createParamSpec(1, 'aPoint', 'tpl-Point', startPoint.name),
            this.createParamSpec(2, 'aPoint', 'tpl-Point', endPoint.name),
        ];
        await remoteConstruct(ctrl, sectorConstruction, pcs, this.editor.geoGateway, 'cung tròn');
    }

    private createParamSpec(indexInCG: number, paramDefId: string, tplStrLangId: string, value: string | number) {
        return {
            indexInCG,
            paramDefId,
            optional: false,
            tplStrLangId,
            params:
                paramDefId === 'aValue'
                    ? { value: { type: 'singleValue', value } }
                    : { name: { type: 'singleValue', value } },
        };
    }
}
