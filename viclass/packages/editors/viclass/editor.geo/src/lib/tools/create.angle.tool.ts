import { Line, line, point, Point, Vector, vector } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, NOT_SET_VALUE, RenderAngle, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pAngle, PreviewQueue, pVertex } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { repeat, stroke, then, ThenSelector, vert, vertex, VertexSelector } from '../selectors';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildAngleConstructionFromLines,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isRenderVertex,
    pickPointName,
    requestElementNames,
} from './tool.utils';

/**
 * Unified tool to create an angle from two lines.
 * Supports line-based angle creation using selector DSL.
 * <AUTHOR>
 */
export class CreateAngleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateAngleTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    renderAngle: RenderAngle | undefined;
    baseLines: RenderLine[] = [];
    lastVertex: VertexSelector;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.renderAngle = undefined;
        this.baseLines = [];
        super.resetState();
    }

    /**
     * Creates the selection logic for two lines + direction point angle creation.
     */
    private createSelLogic() {
        // Create a selector for two lines
        const lineSelector = repeat(
            stroke({
                selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                previewQueue: this.pQ,
                cursor: this.pointerHandler.cursor,
                refinedFilter: (el: RenderLine) => !this.baseLines.includes(el),
            }),
            {
                count: 2,
                onPartialSelection: newSel => {
                    const line = newSel as RenderLine;
                    this.baseLines.push(line);
                    return true;
                },
            }
        );

        // Direction point selector for determining angle orientation
        this.lastVertex = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: (el: RenderVertex) =>
                !this.baseLines.some(line => line.startPointIdx === el.relIndex || line.endPointIdx === el.relIndex),
        });

        // Main selection logic: first select two lines, then direction point
        this.selLogic = then([lineSelector, this.lastVertex], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [lines, directionPoint] = selector.selected;
                this.performConstruction(lines as RenderLine[], directionPoint as RenderVertex, doc);
            },
        });
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        if (selected && selected.length === 2)
            this.previewAngleFromLines(selected[0] as RenderLine[], selected[1] as RenderVertex, ctrl);
        this.pQ.flush(ctrl);
    }

    /**
     * Process lines to extract vectors and flatten lines for calculations
     * This utility function can be reused across different angle creation tools
     */
    private calculateAngleLines(lines: RenderLine[], ctrl: GeoDocCtrl): Line[] {
        return lines.map(l => {
            const s = ctrl.rendererCtrl.elementAt(l.startPointIdx) as RenderVertex;
            const e =
                l.endPointIdx !== NOT_SET_VALUE
                    ? (ctrl.rendererCtrl.elementAt(l.endPointIdx) as RenderVertex)
                    : undefined;

            const fs = point(s.coords[0], s.coords[1]);
            const fe = e ? point(e.coords[0], e.coords[1]) : undefined;

            const fv = fe ? vector(fs, fe) : vector(l.vector[0], l.vector[1]);
            return line(fs, fv.rotate90CW());
        });
    }

    /**
     * Preview angle from two lines with proper vector calculation
     */
    private previewAngleFromLines(lines: RenderLine[], directionPoint: RenderVertex, ctrl: GeoDocCtrl) {
        if (!isRenderVertex(directionPoint)) return;

        try {
            // Get flatten-js lines from render lines
            const fLines = this.calculateAngleLines(lines, ctrl);
            if (fLines.length !== 2) return;

            // Calculate intersection point
            const intersection = fLines[0].intersect(fLines[1]);
            if (!intersection || intersection.length === 0) return;

            const intersectionPoint = intersection[0] as Point;
            const directionPt = point(directionPoint.coords[0], directionPoint.coords[1]);

            // Calculate direction vector from intersection to direction point
            const directionVector = vector(intersectionPoint, directionPt);

            // Get line direction vectors (tangent vectors along the lines)
            // Rotate normal vectors 90 degrees to get tangent vectors
            const line1Tangent = fLines[0].norm.rotate90CCW();
            const line2Tangent = fLines[1].norm.rotate90CCW();

            // Calculate which directions to use based on direction point
            // Use dot product to determine if direction point is on positive or negative side of each line
            const toDir1 = line1Tangent.dot(directionVector) > 0 ? line1Tangent : line1Tangent.multiply(-1);
            const toDir2 = line2Tangent.dot(directionVector) > 0 ? line2Tangent : line2Tangent.multiply(-1);

            // Use cross product to determine counter-clockwise order
            const cross = toDir1.x * toDir2.y - toDir1.y * toDir2.x;

            let startVector: Vector, endVector: Vector;

            // If cross product > 0, toDir2 is counter-clockwise from toDir1
            // If cross product < 0, toDir1 is counter-clockwise from toDir2
            if (cross > 0) {
                // toDir2 is counter-clockwise from toDir1, so start with toDir1
                startVector = toDir1;
                endVector = toDir2;
            } else {
                // toDir1 is counter-clockwise from toDir2, so start with toDir2
                startVector = toDir2;
                endVector = toDir1;
            }

            // Create preview
            const anglePreview = pAngle(ctrl, -24, intersectionPoint, startVector, endVector);
            this.pQ.add(anglePreview);
        } catch (error) {
            console.warn('Error in previewAngleFromLines:', error);
            return;
        }
    }

    /**
     * Main construction method for line-based angle creation
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(lines: RenderLine[], directionPoint: RenderVertex, ctrl: GeoDocCtrl) {
        try {
            await this.performConstructionFromLines(lines, directionPoint, ctrl);
        } catch (err: unknown) {
            this.resetState();
            console.error(err);
            throw err;
        }
    }

    /**
     * Create angle from two lines with proper direction calculation
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstructionFromLines(lines: RenderLine[], directionPoint: RenderVertex, ctrl: GeoDocCtrl) {
        const dirPt = isRenderVertex(directionPoint) ? directionPoint : vert(directionPoint);

        try {
            // Get flatten-js lines from render lines
            const fLines = this.calculateAngleLines(lines, ctrl);
            if (fLines.length !== 2) return;

            // Calculate intersection point
            const intersection = fLines[0].intersect(fLines[1]);
            if (!intersection || intersection.length === 0) return;

            const intersectionPoint = intersection[0] as Point;
            const directionPt = point(dirPt.coords[0], dirPt.coords[1]);

            // Calculate direction vector from intersection to direction point
            const directionVector = vector(intersectionPoint, directionPt);

            // Get line direction vectors (tangent vectors along the lines)
            // Rotate normal vectors 90 degrees to get tangent vectors
            const line1Tangent = fLines[0].norm.rotate90CCW();
            const line2Tangent = fLines[1].norm.rotate90CCW();

            // Calculate which directions to use based on direction point
            // Use dot product to determine if direction point is on positive or negative side of each line
            const toDir1 = line1Tangent.dot(directionVector) > 0 ? line1Tangent : line1Tangent.multiply(-1);
            const toDir2 = line2Tangent.dot(directionVector) > 0 ? line2Tangent : line2Tangent.multiply(-1);

            // Use cross product to determine counter-clockwise order
            const cross = toDir1.x * toDir2.y - toDir1.y * toDir2.x;

            let startVector: Vector, endVector: Vector, startLine: RenderLine, endLine: RenderLine;

            // If cross product > 0, toDir2 is counter-clockwise from toDir1
            // If cross product < 0, toDir1 is counter-clockwise from toDir2
            if (cross > 0) {
                // toDir2 is counter-clockwise from toDir1, so start with toDir1
                startVector = toDir1;
                endVector = toDir2;
                startLine = lines[0];
                endLine = lines[1];
            } else {
                // toDir1 is counter-clockwise from toDir2, so start with toDir2
                startVector = toDir2;
                endVector = toDir1;
                startLine = lines[1];
                endLine = lines[0];
            }

            // Calculate direction values for construction
            const startLineIndex = lines.indexOf(startLine);
            const endLineIndex = lines.indexOf(endLine);

            const startFLine = fLines[startLineIndex];
            const endFLine = fLines[endLineIndex];

            // Get tangent vectors for direction calculation
            const startLineTangent = startFLine.norm.rotate90CCW();
            const endLineTangent = endFLine.norm.rotate90CCW();

            // Calculate start direction: 1 if same direction as line, -1 if opposite
            const startDirection = startLineTangent.dot(startVector) > 0 ? 1 : -1;
            const endDirection = endLineTangent.dot(endVector) > 0 ? 1 : -1;

            // Get angle name from user
            const vertexAtIntersection = pVertex(-25, [intersectionPoint.x, intersectionPoint.y]);
            const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
            const nameInputs = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Tên góc',
                        originElement: [vertexAtIntersection],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];

            if (!nameInputs.length) return;
            const angleName = nameInputs[0];

            // Create angle construction
            const angleConstruction = buildAngleConstructionFromLines(
                startLine.name,
                startLine.elType,
                endLine.name,
                endLine.elType,
                angleName,
                startDirection,
                endDirection
            );

            this.resetState();

            await ctrl.editor.awarenessFeature.useAwareness(
                ctrl.viewport.id,
                'Đang tạo góc',
                buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
                async () => {
                    const constructResponse = await constructExec(() =>
                        this.editor.geoGateway.construct(ctrl.state.globalId, [{ construction: angleConstruction }])
                    );

                    await syncRenderCommands(constructResponse.render, ctrl);
                    addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                }
            );
        } catch (error) {
            console.error('Error in performConstructionFromLines:', error);
            this.resetState();
            throw error;
        }
    }
}
