import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator } from '@viclass/editor.core';
import { syncRenderCommands, syncRemovePreviewCmd } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    RenderAngle,
    RenderLine,
    RenderRay,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pRay, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { or, stroke, vertex, vertexOnStroke } from '../selectors';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    pickPointName,
    requestElementNames,
} from './tool.utils';

/**
 * Bisector Line Tool - Creates bisector lines from angles using selector DSL
 *
 * Optimized with flatten operations for geometry calculations:
 * - Uses flatten: 1 in selectors for optimal performance
 * - Implements flattened vector operations for coordinate calculations
 * - Provides helper methods for consistent geometry operations
 * - Validates coordinates using flattened access patterns
 *
 * <AUTHOR>
 */
export class CreateBisectorLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateBisectorLineTool';

    private pQ = new PreviewQueue();
    private selectedAngle: RenderAngle | undefined;
    private bisectorVector: number[] | undefined;
    private selectionStep: 'angle' | 'endpoint' = 'angle';
    private endSelector: any;
    private rayPreview: RenderRay | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
    }

    override resetState() {
        this.endSelector?.reset();
        this.selectedAngle = undefined;
        this.bisectorVector = undefined;
        this.selectionStep = 'angle';
        this.rayPreview = undefined;
        super.resetState();
    }

    /**
     * Helper method to flatten and validate geometry coordinates
     */
    private flattenAndValidateCoords(coords: number[] | undefined): [number, number] | null {
        if (!coords || coords.length < 2) return null;
        const [x, y] = coords;
        return isFinite(x) && isFinite(y) ? [x, y] : null;
    }

    /**
     * Helper method to perform flattened vector operations
     */
    private flattenVectorOperation(
        operation: 'dot' | 'magnitude' | 'normalize' | 'subtract',
        vectorA: number[],
        vectorB?: number[]
    ): number | number[] {
        const [ax, ay] = vectorA;

        switch (operation) {
            case 'dot':
                if (!vectorB) throw new Error('Vector B required for dot product');
                const [bx, by] = vectorB;
                return ax * bx + ay * by;

            case 'magnitude':
                return Math.sqrt(ax * ax + ay * ay);

            case 'normalize':
                const mag = Math.sqrt(ax * ax + ay * ay);
                return mag > 0 ? [ax / mag, ay / mag] : [0, 0];

            case 'subtract':
                if (!vectorB) throw new Error('Vector B required for subtraction');
                const [bx2, by2] = vectorB;
                return [ax - bx2, ay - by2];

            default:
                throw new Error(`Unknown vector operation: ${operation}`);
        }
    }

    /**
     * Calculate bisector vector from two angle vectors using optimized flatten operations
     */
    private calculateBiSectorVectorOfAngle(a: number[], b: number[]): number[] {
        // Use flattened vector operations for better performance
        const dotProduct = this.flattenVectorOperation('dot', a, b) as number;
        const magnitudeA = this.flattenVectorOperation('magnitude', a) as number;
        const magnitudeB = this.flattenVectorOperation('magnitude', b) as number;

        // Early return for zero vectors to avoid division by zero
        if (magnitudeA === 0 || magnitudeB === 0) {
            return [0, 0];
        }

        // Calculate the cosine of the angle between a and b
        const cosTheta = dotProduct / (magnitudeA * magnitudeB);

        // Calculate the sine of the angle between a and b with bounds checking
        const sinThetaSquared = Math.max(0, 1 - cosTheta * cosTheta);
        const sinTheta = Math.sqrt(sinThetaSquared);

        // Avoid division by zero for parallel vectors
        if (sinTheta === 0) {
            // Return normalized perpendicular vector for parallel case
            const [ax, ay] = a;
            return [-ay / magnitudeA, ax / magnitudeA];
        }

        // Calculate the x and y components of the angle bisector vector using flattened operations
        const [ax, ay] = a;
        const [bx, by] = b;
        const cX = (magnitudeA * by - magnitudeB * ay) / (2 * sinTheta);
        const cY = (magnitudeB * ax - magnitudeA * bx) / (2 * sinTheta);

        return [cX, cY];
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        if (this.selectionStep === 'angle') {
            this.handleAngleSelection(event, ctrl);
        } else if (this.selectionStep === 'endpoint' && this.endSelector) {
            this.endSelector.trySelect(event, ctrl);
        }
        this.pQ.flush(ctrl);
    }

    /**
     * Handle angle selection manually
     */
    private handleAngleSelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        if (event.eventType === 'pointerup') {
            const { hitEl } = this.posAndCtrl(event);
            if (hitEl && hitEl.type === 'RenderAngle') {
                this.selectedAngle = hitEl as RenderAngle;
                this.bisectorVector = this.calculateBiSectorVectorOfAngle(
                    this.selectedAngle.vectorStart,
                    this.selectedAngle.vectorEnd
                );

                // Show ray preview of the bisector
                this.showBisectorRayPreview(ctrl);

                // Move to endpoint selection step
                this.selectionStep = 'endpoint';
                this.createEndpointSelector(ctrl);
            }
        }
    }

    /**
     * Show ray preview of the bisector line with optimized geometry operations
     */
    private showBisectorRayPreview(ctrl: GeoDocCtrl) {
        if (!this.selectedAngle || !this.bisectorVector) return;

        // Remove existing ray preview if any
        if (this.rayPreview) {
            syncRemovePreviewCmd(this.rayPreview, ctrl);
        }

        // Get the angle vertex point using flattened access
        const anglePoint = ctrl.rendererCtrl.elementAt(this.selectedAngle.anglePointIdx) as RenderVertex;
        if (!anglePoint?.coords) return;

        // Use helper method to validate and flatten coordinates
        const startCoords = this.flattenAndValidateCoords(anglePoint.coords);
        if (!startCoords) return;

        // Normalize bisector vector using flattened vector operations
        const normalizedVector = this.flattenVectorOperation('normalize', this.bisectorVector) as number[];

        // Create ray preview using pRay function with flattened coordinates
        this.rayPreview = pRay(ctrl, -20, startCoords, undefined, normalizedVector);

        // Add to preview queue and flush efficiently
        this.pQ.add(this.rayPreview);
        this.pQ.flush(ctrl);
    }

    /**
     * Create selector for endpoint selection with optimized flatten configuration
     */
    private createEndpointSelector(ctrl: GeoDocCtrl) {
        this.endSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                stroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
            ],
            {
                // Use flatten with level 1 for optimal performance in geometry operations
                // This flattens nested selections but maintains type safety
                flatten: 1,
                onComplete: async (selector, doc) => {
                    // Handle flattened selection results more efficiently
                    let endElement: RenderVertex | RenderLine | undefined;

                    if (Array.isArray(selector.selected)) {
                        // Flatten operation ensures we get a flat array
                        endElement = selector.selected[0] as RenderVertex | RenderLine;
                    } else {
                        endElement = selector.selected as RenderVertex | RenderLine;
                    }

                    if (endElement) {
                        await this.performConstruction(doc, endElement);
                    }
                },
            }
        );

        // If no endpoint is selected after a timeout, create simple bisector
        setTimeout(() => {
            if (this.selectionStep === 'endpoint' && this.selectedAngle) {
                this.performConstruction(ctrl);
            }
        }, 5000); // 5 second timeout
    }

    /**
     * Performs the bisector construction based on selected elements
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(ctrl: GeoDocCtrl, endElement?: RenderVertex | RenderLine) {
        if (!this.selectedAngle || !this.bisectorVector) return;

        try {
            const angleName = this.selectedAngle.name;
            let construction: GeoElConstructionRequest;

            if (!endElement) {
                // Simple bisector line
                construction = this.buildBisectorConstruction(angleName);
            } else if (endElement.type === 'RenderVertex') {
                // Bisector segment to point
                const pointEnd = endElement as RenderVertex;

                // Request name for the point if it's a preview point
                if (pointEnd.relIndex < 0) {
                    const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
                    const inputPointNames = (
                        await requestElementNames(ctrl, nt, [
                            {
                                objName: 'Tên điểm cuối phân giác',
                                originElement: [pointEnd],
                                pickName: pickPointName,
                                namesToAvoid: [],
                            },
                        ])
                    )[0];

                    if (!inputPointNames.length) {
                        this.resetState();
                        return;
                    }
                    pointEnd.name = inputPointNames[0];
                }

                // Calculate scaling factor for segment
                const anglePoint = ctrl.rendererCtrl.elementAt(this.selectedAngle.anglePointIdx) as RenderVertex;
                const k = this.calculateScalingFactor(this.bisectorVector, anglePoint.coords, pointEnd.coords);

                construction = this.buildBisectorSegmentConstruction(pointEnd.name, angleName, k);
            } else {
                // Bisector segment with intersection line
                const intersectLine = endElement as RenderLine;
                construction = this.buildBisectorSegmentAndIntersectionLineConstruction(
                    angleName,
                    intersectLine.name,
                    intersectLine.elType
                );
            }

            // Clear ray preview before construction
            if (this.rayPreview) {
                await syncRemovePreviewCmd(this.rayPreview, ctrl);
                this.rayPreview = undefined;
            }

            await ctrl.editor.awarenessFeature.useAwareness(
                ctrl.viewport.id,
                'Đang tạo đường phân giác',
                buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
                async () => {
                    const constructions: ConstructionRequest[] = [];

                    // Add point construction if needed
                    if (endElement?.type === 'RenderVertex' && endElement.relIndex < 0) {
                        const vertex = endElement as RenderVertex;
                        constructions.push({
                            construction: buildPointConstruction(vertex.name, {
                                x: vertex.coords[0],
                                y: vertex.coords[1],
                            }),
                        });
                    }

                    constructions.push({ construction });

                    const constructResponse = await constructExec(() =>
                        this.editor.geoGateway.construct(ctrl.state.globalId, constructions)
                    );

                    await syncRenderCommands(constructResponse.render, ctrl);
                    addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                }
            );
        } catch (error) {
            console.error('Error in bisector construction:', error);
            this.resetState();
            throw error;
        }
    }

    /**
     * Calculate scaling factor for bisector segment using flattened geometry operations
     */
    private calculateScalingFactor(bisectorVector: number[], startPoint: number[], endPoint: number[]): number {
        // Use flattened vector operations for better performance
        const distanceVector = this.flattenVectorOperation('subtract', endPoint, startPoint) as number[];
        const distance = this.flattenVectorOperation('magnitude', distanceVector) as number;
        const vectorMagnitude = this.flattenVectorOperation('magnitude', bisectorVector) as number;

        // Avoid division by zero
        if (vectorMagnitude === 0) {
            return 0;
        }

        return distance / vectorMagnitude;
    }

    // Construction methods - keeping the original selector actions

    private buildBisectorConstruction(angleName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngle');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentConstruction(name: string, angleName: string, k: number): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngleSegment');
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentAndIntersectionLineConstruction(
        angleName: string,
        intersectionLineName: string,
        intersectionLineType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/BisectorOfAngleEC',
            'LineVi',
            'BisectorAngleSegmentWithIntersectionLine'
        );

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
