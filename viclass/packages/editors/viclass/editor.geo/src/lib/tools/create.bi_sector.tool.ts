import { point, vector, Vector, Point } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    RenderAngle,
    RenderLine,
    RenderRay,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { or, stroke, then, ThenSelector, vertex, vertexOnStroke } from '../selectors';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    pickPointName,
    requestElementNames,
} from './tool.utils';

/**
 * Bisector Line Tool - Creates bisector lines from angles using selector DSL
 * <AUTHOR>
 */
export class CreateBisectorLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateBisectorLineTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedAngle: RenderAngle | undefined;
    bisectorVector: number[] | undefined;
    rayPreview: RenderRay | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedAngle = undefined;
        this.bisectorVector = undefined;
        this.rayPreview = undefined;
        super.resetState();
    }

    /**
     * Creates the selection logic for angle + optional endpoint selection.
     */
    private createSelLogic() {
        // First selector: select an angle directly
        const angleSelector = stroke({
            selectableStrokeTypes: ['RenderAngle'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Second selector: optional endpoint selection
        const endpointSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                stroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
            ],
            {
                flatten: 1,
            }
        );

        // Main selection logic: first select angle, then optional endpoint
        this.selLogic = then([angleSelector, endpointSelector], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [angle, endElement] = selector.selected;
                this.performConstruction(doc, angle as RenderAngle, endElement);
            },
        });
    }

    /**
     * Helper method to create Point from coordinates using @flatten-js/core
     */
    private createPoint(coords: number[] | undefined): Point | null {
        if (!coords || coords.length < 2) return null;
        const [x, y] = coords;
        return isFinite(x) && isFinite(y) ? point(x, y) : null;
    }

    /**
     * Helper method to create Vector from coordinates using @flatten-js/core
     */
    private createVector(coords: number[]): Vector {
        const [x, y] = coords;
        return vector(x, y);
    }

    /**
     * Calculate bisector vector from two angle vectors using @flatten-js/core
     */
    private calculateBiSectorVectorOfAngle(a: number[], b: number[]): number[] {
        // Create vectors using @flatten-js/core
        const vectorA = this.createVector(a);
        const vectorB = this.createVector(b);

        const dotProduct = vectorA.dot(vectorB);
        const magnitudeA = vectorA.length;
        const magnitudeB = vectorB.length;

        // Early return for zero vectors to avoid division by zero
        if (magnitudeA === 0 || magnitudeB === 0) {
            return [0, 0];
        }

        // Calculate the cosine of the angle between a and b
        const cosTheta = dotProduct / (magnitudeA * magnitudeB);

        // Calculate the sine of the angle between a and b with bounds checking
        const sinThetaSquared = Math.max(0, 1 - cosTheta * cosTheta);
        const sinTheta = Math.sqrt(sinThetaSquared);

        // Avoid division by zero for parallel vectors
        if (sinTheta === 0) {
            // Return normalized perpendicular vector for parallel case
            const normalizedA = vectorA.normalize();
            return [-normalizedA.y, normalizedA.x];
        }

        // Calculate the x and y components of the angle bisector vector
        const cX = (magnitudeA * vectorB.y - magnitudeB * vectorA.y) / (2 * sinTheta);
        const cY = (magnitudeB * vectorA.x - magnitudeA * vectorB.x) / (2 * sinTheta);

        return [cX, cY];
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    /**
     * Performs the bisector construction based on selected elements
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(ctrl: GeoDocCtrl, angle: RenderAngle, endElement?: any) {
        try {
            // Store selected angle and calculate bisector vector
            this.selectedAngle = angle;
            this.bisectorVector = this.calculateBiSectorVectorOfAngle(
                this.selectedAngle.vectorStart,
                this.selectedAngle.vectorEnd
            );

            const angleName = this.selectedAngle.name;
            let construction: GeoElConstructionRequest;

            if (!endElement) {
                // Simple bisector line
                construction = this.buildBisectorConstruction(angleName);
            } else if (endElement.type === 'RenderVertex') {
                // Bisector segment to point
                const pointEnd = endElement as RenderVertex;

                // Request name for the point if it's a preview point
                if (pointEnd.relIndex < 0) {
                    const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
                    const inputPointNames = (
                        await requestElementNames(ctrl, nt, [
                            {
                                objName: 'Tên điểm cuối phân giác',
                                originElement: [pointEnd],
                                pickName: pickPointName,
                                namesToAvoid: [],
                            },
                        ])
                    )[0];

                    if (!inputPointNames.length) {
                        this.resetState();
                        return;
                    }
                    pointEnd.name = inputPointNames[0];
                }

                // Calculate scaling factor for segment
                const anglePoint = ctrl.rendererCtrl.elementAt(this.selectedAngle.anglePointIdx) as RenderVertex;
                const k = this.calculateScalingFactor(this.bisectorVector, anglePoint.coords, pointEnd.coords);

                construction = this.buildBisectorSegmentConstruction(pointEnd.name, angleName, k);
            } else {
                // Bisector segment with intersection line
                const intersectLine = endElement as RenderLine;
                construction = this.buildBisectorSegmentAndIntersectionLineConstruction(
                    angleName,
                    intersectLine.name,
                    intersectLine.elType
                );
            }

            await ctrl.editor.awarenessFeature.useAwareness(
                ctrl.viewport.id,
                'Đang tạo đường phân giác',
                buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
                async () => {
                    const constructions: ConstructionRequest[] = [];

                    // Add point construction if needed
                    if (endElement?.type === 'RenderVertex' && endElement.relIndex < 0) {
                        const vertex = endElement as RenderVertex;
                        constructions.push({
                            construction: buildPointConstruction(vertex.name, {
                                x: vertex.coords[0],
                                y: vertex.coords[1],
                            }),
                        });
                    }

                    constructions.push({ construction });

                    const constructResponse = await constructExec(() =>
                        this.editor.geoGateway.construct(ctrl.state.globalId, constructions)
                    );

                    await syncRenderCommands(constructResponse.render, ctrl);
                    addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                }
            );
        } catch (error) {
            console.error('Error in bisector construction:', error);
            this.resetState();
            throw error;
        }
    }

    /**
     * Calculate scaling factor for bisector segment using @flatten-js/core
     */
    private calculateScalingFactor(bisectorVector: number[], startPoint: number[], endPoint: number[]): number {
        // Create Points and Vectors using @flatten-js/core
        const startPt = this.createPoint(startPoint);
        const endPt = this.createPoint(endPoint);
        const bisectorVec = this.createVector(bisectorVector);

        if (!startPt || !endPt) {
            return 0;
        }

        // Calculate distance using @flatten-js/core
        const distance = startPt.distanceTo(endPt)[0];
        const vectorMagnitude = bisectorVec.length;

        // Avoid division by zero
        if (vectorMagnitude === 0) {
            return 0;
        }

        return distance / vectorMagnitude;
    }

    // Construction methods - keeping the original selector actions

    private buildBisectorConstruction(angleName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngle');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentConstruction(name: string, angleName: string, k: number): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngleSegment');
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentAndIntersectionLineConstruction(
        angleName: string,
        intersectionLineName: string,
        intersectionLineType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/BisectorOfAngleEC',
            'LineVi',
            'BisectorAngleSegmentWithIntersectionLine'
        );

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
