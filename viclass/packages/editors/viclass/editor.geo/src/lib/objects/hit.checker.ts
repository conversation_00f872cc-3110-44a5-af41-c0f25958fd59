import { point, vector } from '@flatten-js/core';
import { Position, SelectHitContext, SelectHitContextDetails } from '@viclass/editor.core';
import {
    FillType,
    GeoRenderElement,
    RenderAngle,
    RenderCircleShape,
    RenderEllipse,
    RenderEllipseShape,
    RenderLine,
    RenderPolygon,
    RenderSector,
    RenderSectorShape,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeoRenderer } from '../renderer';
import { distance2Point } from '../tools/tool.utils';
import { GeoDocCtrl } from './geo.document.ctrl';

export interface GeoHitContextDetails extends SelectHitContextDetails {
    el: GeoRenderElement;
    hitMargin: number;
    epsilon?: number;
}

export interface GeoSelectHitContext extends SelectHitContext {
    doc: GeoDocCtrl;
    hitDetails: GeoHitContextDetails;
}

export function EPSILON(base: number, unit: number, zoom: number): number {
    return (base / unit) * zoom;
}

export function checkHitOnElementBoundary2D(
    posInGeo: Position,
    el: GeoRenderElement,
    renderer: GeoRenderer,
    doc: GeoDocCtrl,
    zoomLevel: number,
    epsilon: number
): GeoSelectHitContext | undefined {
    const docRenderProp = doc.state.docRenderProp;
    const lineWeight = el.renderProp['lineWeight'] ?? 1;
    const e = EPSILON(lineWeight / 2, docRenderProp.screenUnit * docRenderProp.scale, zoomLevel) + epsilon;
    switch (el.type) {
        case 'RenderVertex':
            return checkHitOnVertex2D(posInGeo, el as RenderVertex, doc, e);
        case 'RenderLineSegment':
        case 'RenderVector':
        case 'RenderRay':
        case 'RenderLine':
            return checkHitOnLine2D(posInGeo, el as RenderLine, doc, renderer, e);
        case 'RenderCircle':
            return checkHitOnCircle2D(posInGeo, el as RenderSector, doc, renderer, e);
        case 'RenderSector':
            return checkHitOnCircularSector2D(posInGeo, el as RenderSector, doc, renderer, zoomLevel);
        case 'RenderEllipse':
            return checkHitOnEllipse2D(posInGeo, el as RenderEllipse, doc, renderer, e);
        case 'RenderAngle':
            return checkHitOnAngle2D(posInGeo, el as RenderAngle, doc, renderer, e);
        default:
            break;
    }

    return undefined;
}

export function checkHitOnElementsBoundary2D(
    doc: GeoDocCtrl,
    els: GeoRenderElement[],
    posInGeo: Position,
    renderer: GeoRenderer,
    zoomLevel: number,
    epsilon?: number
): GeoSelectHitContext[] {
    return els
        .filter(el => doc.editor.filterElementFunc(el) && (el as StrokeType).length !== undefined)
        .sort((a, b) => ((a as StrokeType).length > (b as StrokeType).length ? 1 : -1))
        .map(el => checkHitOnElementBoundary2D(posInGeo, el, renderer, doc, zoomLevel, epsilon))
        .filter(h => h && true);
}

export function checkHitOnPoints(
    doc: GeoDocCtrl,
    els: GeoRenderElement[],
    posInGeo: Position,
    renderer: GeoRenderer,
    zoomLevel: number,
    epsilon?: number
): GeoSelectHitContext[] {
    return els
        .filter(el => doc.editor.filterElementFunc(el) && el instanceof RenderVertex)
        .map(el => checkHitOnElementBoundary2D(posInGeo, el, renderer, doc, zoomLevel, epsilon))
        .filter(h => h && true);
}

export function checkHitOnElementsInside2D(
    doc: GeoDocCtrl,
    els: GeoRenderElement[],
    posInGeo: Position,
    renderer: GeoRenderer
): GeoSelectHitContext[] {
    return els
        .filter(el => doc.editor.filterElementFunc(el) && (el as FillType).area !== undefined)
        .sort((a, b) => ((a as FillType).area > (b as FillType).area ? 1 : -1))
        .map(el => checkHitOnElementInside2D(posInGeo, el, renderer, doc))
        .filter(h => h && true);
}

export function checkHitOnElementInside2D(
    posInGeo: Position,
    el: GeoRenderElement,
    renderer: GeoRenderer,
    doc: GeoDocCtrl
): GeoSelectHitContext {
    switch (el.type) {
        case 'RenderPolygon':
            return checkHitInsidePolygon2D(posInGeo, el as RenderPolygon, doc, renderer);
        case 'RenderSectorShape':
            return checkHitInsideCircularSector2D(posInGeo, el as RenderSectorShape, doc, renderer);
        case 'RenderCircleShape':
            return checkHitInsideCircle2D(posInGeo, el as RenderCircleShape, doc, renderer);
        case 'RenderEllipseShape':
            return checkHitInsideEllipse2D(posInGeo, el as RenderEllipseShape, doc, renderer);
        default:
            break;
    }

    return undefined;
}

function checkHitOnVertex2D(
    posInGeo: Position,
    rel: RenderVertex,
    doc: GeoDocCtrl,
    epsilon: number
): GeoSelectHitContext {
    const d = Math.sqrt(Math.pow(rel.coords[0] - posInGeo.x, 2) + Math.pow(rel.coords[1] - posInGeo.y, 2));

    if (d <= epsilon) {
        const hitDetails: GeoHitContextDetails = {
            hitId: rel.relIndex,
            el: rel,
            hitMargin: d,
            epsilon: epsilon,
        };

        return {
            doc: doc,
            hitDetails: hitDetails,
        };
    }

    return undefined;
}

function checkHitOnLine2D(
    posInGeo: Position,
    line: RenderLine,
    doc: GeoDocCtrl,
    renderer: GeoRenderer,
    epsilon: number
): GeoSelectHitContext {
    const startPoint = renderer.elementAt(line.startPointIdx) as RenderVertex;
    const n = [-line.vector[1], line.vector[0]];
    const a = n[0];
    const b = n[1];

    if (a == 0 && b == 0) return undefined;

    const c = -a * startPoint.coords[0] - b * startPoint.coords[1];

    // distance from the point to line
    const d = Math.abs(a * posInGeo.x + b * posInGeo.y + c) / Math.sqrt(a * a + b * b);
    if (d >= epsilon) return undefined;

    let result: GeoSelectHitContext;
    switch (line.type) {
        case 'RenderLine': {
            result = {
                doc: doc,
                hitDetails: {
                    hitId: line.relIndex,
                    el: line,
                    hitMargin: d,
                    epsilon: epsilon,
                } as GeoHitContextDetails,
            };
            break;
        }
        case 'RenderVector':
        case 'RenderLineSegment': {
            const endPoint = renderer.elementAt(line.endPointIdx) as RenderVertex;
            const dS = Math.sqrt(
                Math.pow(posInGeo.x - startPoint.coords[0], 2) + Math.pow(posInGeo.y - startPoint.coords[1], 2)
            );
            const dE = Math.sqrt(
                Math.pow(posInGeo.x - endPoint.coords[0], 2) + Math.pow(posInGeo.y - endPoint.coords[1], 2)
            );
            const d0 = Math.sqrt(
                Math.pow(startPoint.coords[0] - endPoint.coords[0], 2) +
                    Math.pow(startPoint.coords[1] - endPoint.coords[1], 2)
            );
            if (dS + dE - d0 < epsilon) {
                result = {
                    doc: doc,
                    hitDetails: {
                        hitId: line.relIndex,
                        el: line,
                        hitMargin: d,
                        epsilon: epsilon,
                    } as GeoHitContextDetails,
                };
            }
            break;
        }
        case 'RenderRay': {
            const endPointCoord = [startPoint.coords[0] + line.vector[0], startPoint.coords[1] + line.vector[1]];
            const projectionPointCoord = [
                (b * (b * posInGeo.x - a * posInGeo.y) - a * c) / (a * a + b * b),
                (a * (-b * posInGeo.x + a * posInGeo.y) - b * c) / (a * a + b * b),
            ];

            const d1 = endPointCoord[1] - startPoint.coords[1];
            const d2 = projectionPointCoord[1] - startPoint.coords[1];
            if (d1 * d2 > 0) {
                result = {
                    doc: doc,
                    hitDetails: {
                        hitId: line.relIndex,
                        el: line,
                        hitMargin: d,
                        epsilon: epsilon,
                    } as GeoHitContextDetails,
                };
            }
            break;
        }
        default:
            break;
    }

    return result;
}

function checkHitInsidePolygon2D(
    posInGeo: Position,
    polygon: RenderPolygon,
    doc: GeoDocCtrl,
    renderer: GeoRenderer
): GeoSelectHitContext {
    let wn = 0; // winding number

    const points = polygon.faces.map(i => renderer.elementAt(i) as RenderVertex);

    points.forEach((a, i) => {
        const b = points[(i + 1) % points.length];
        if (a.coords[1] <= posInGeo.y) {
            if (b.coords[1] > posInGeo.y && cross(a, b, posInGeo) > 0) {
                wn += 1;
            }
        } else if (b.coords[1] <= posInGeo.y && cross(a, b, posInGeo) < 0) {
            wn -= 1;
        }
    });

    if (wn !== 0) {
        return {
            doc: doc,
            hitDetails: {
                hitId: polygon.relIndex,
                el: polygon,
                hitMargin: 0,
            } as GeoHitContextDetails,
        };
    }

    return undefined;
}

function checkHitOnCircularSector2D(
    posInGeo: Position,
    sector: RenderSector,
    doc: GeoDocCtrl,
    renderer: GeoRenderer,
    epsilon: number
): GeoSelectHitContext {
    const vS = renderer.elementAt<RenderVertex>(sector.startPointIdx).coords;
    const vE = renderer.elementAt<RenderVertex>(sector.endPointIdx).coords;
    const vC = renderer.elementAt<RenderVertex>(sector.centerPointIdx).coords;
    const pS = point(vS[0], vS[1]);
    const pE = point(vE[0], vE[1]);
    const pC = point(vC[0], vC[1]);
    const pMouse = point(posInGeo.x, posInGeo.y);
    const radius = sector.radius;
    const vec0 = vector(pC, point(pC.x + 10, pC.y));
    const vecS = vector(pC, pS);
    const vecE = vector(pC, pE);
    const vecMouse = vector(pC, pMouse);
    const startAngle = vec0.angleTo(vecS);
    let endAngle = vec0.angleTo(vecE);
    let mouseAngle = vec0.angleTo(vecMouse);
    if (startAngle > endAngle) {
        endAngle += 2 * Math.PI;
        if (pMouse.y >= pC.y) {
            mouseAngle += 2 * Math.PI;
        }
    }

    let d = pC.distanceTo(pMouse)[0];

    d = Math.abs(d - radius);

    if (d < epsilon && startAngle <= mouseAngle && mouseAngle <= endAngle) {
        const hitDetails: GeoHitContextDetails = {
            hitId: sector.relIndex,
            el: sector,
            hitMargin: d,
            epsilon: epsilon,
        };

        return {
            doc: doc,
            hitDetails: hitDetails,
        };
    }

    return undefined;
}

function checkHitInsideCircularSector2D(
    posInGeo: Position,
    sector: RenderSectorShape,
    doc: GeoDocCtrl,
    renderer: GeoRenderer
): GeoSelectHitContext {
    const vS = renderer.elementAt<RenderVertex>(sector.startPointIdx).coords;
    const vE = renderer.elementAt<RenderVertex>(sector.endPointIdx).coords;
    const vC = renderer.elementAt<RenderVertex>(sector.centerPointIdx).coords;
    const pS = point(vS[0], vS[1]);
    const pE = point(vE[0], vE[1]);
    const pC = point(vC[0], vC[1]);
    const pMouse = point(posInGeo.x, posInGeo.y);
    const radius = sector.radius;
    const vec0 = vector(pC, point(pC.x + 10, pC.y));
    const vecS = vector(pC, pS);
    const vecE = vector(pC, pE);
    const vecMouse = vector(pC, pMouse);
    const startAngle = vec0.angleTo(vecS);
    let endAngle = vec0.angleTo(vecE);
    let mouseAngle = vec0.angleTo(vecMouse);
    if (startAngle > endAngle) {
        endAngle += 2 * Math.PI;
        if (pMouse.y >= pC.y) {
            mouseAngle += 2 * Math.PI;
        }
    }

    const d = pC.distanceTo(pMouse)[0];

    if (d <= radius && startAngle <= mouseAngle && mouseAngle <= endAngle) {
        const hitDetails: GeoHitContextDetails = {
            hitId: sector.relIndex,
            el: sector,
            hitMargin: d,
        };

        return {
            doc: doc,
            hitDetails: hitDetails,
        };
    }

    return undefined;
}

function checkHitOnCircle2D(
    posInGeo: Position,
    circle: RenderSector,
    doc: GeoDocCtrl,
    renderer: GeoRenderer,
    epsilon: number
): GeoSelectHitContext {
    const c = renderer.elementAt(circle.centerPointIdx) as RenderVertex;

    let d = distance2Point(c.coords, [posInGeo.x, posInGeo.y]);

    d = Math.abs(d - circle.radius);

    if (d < epsilon) {
        const hitDetails: GeoHitContextDetails = {
            hitId: circle.relIndex,
            el: circle,
            hitMargin: d,
            epsilon: epsilon,
        };

        return {
            doc: doc,
            hitDetails: hitDetails,
        };
    }

    return undefined;
}

function checkHitInsideCircle2D(
    posInGeo: Position,
    circle: RenderCircleShape,
    doc: GeoDocCtrl,
    renderer: GeoRenderer
): GeoSelectHitContext {
    const c = renderer.elementAt(circle.centerPointIdx) as RenderVertex;

    const d = Math.sqrt(Math.pow(c.coords[0] - posInGeo.x, 2) + Math.pow(c.coords[1] - posInGeo.y, 2));

    if (d <= circle.radius) {
        const hitDetails: GeoHitContextDetails = {
            hitId: circle.relIndex,
            el: circle,
            hitMargin: d,
        };

        return {
            doc: doc,
            hitDetails: hitDetails,
        };
    }

    return undefined;
}

function _pointBelongsToAngle(angle: RenderAngle, vectorPoint: number[]): boolean {
    // Use cross product method to determine if point is within the angle
    // This is more reliable than angle comparison for geometric calculations

    const vStart = angle.vectorStart;
    const vEnd = angle.vectorEnd;
    const vPoint = vectorPoint;

    // Calculate cross products to determine orientation
    const crossStart = vStart[0] * vPoint[1] - vStart[1] * vPoint[0];
    const crossEnd = vEnd[0] * vPoint[1] - vEnd[1] * vPoint[0];
    const crossStartEnd = vStart[0] * vEnd[1] - vStart[1] * vEnd[0];

    // If the angle is reflex (> 180 degrees), the logic is different
    if (crossStartEnd >= 0) {
        // Non-reflex angle: point is inside if it's on the correct side of both vectors
        return crossStart >= 0 && crossEnd <= 0;
    } else {
        // Reflex angle: point is inside if it's on the correct side of either vector
        return crossStart >= 0 || crossEnd <= 0;
    }
}

function checkHitOnAngle2D(
    posInGeo: Position,
    angle: RenderAngle,
    doc: GeoDocCtrl,
    renderer: GeoRenderer,
    epsilon: number
): GeoSelectHitContext {
    const anglePoint = (renderer.elementAt(angle.anglePointIdx) as RenderVertex).coords;

    // Calculate the distance from the mouse position to the angle vertex
    const d = Math.sqrt(Math.pow(anglePoint[0] - posInGeo.x, 2) + Math.pow(anglePoint[1] - posInGeo.y, 2));

    // Use the same radius calculation logic as in the renderer
    const minSideLineSegmenGeotLength = renderer.getGeoMinLengthOfAngleSide(angle);
    const spaceFromArcToCorner = angle.renderProp.spaceFromArcToCorner;
    const radius =
        minSideLineSegmenGeotLength !== undefined
            ? Math.min(spaceFromArcToCorner, renderer.geoToLayerLength(minSideLineSegmenGeotLength))
            : spaceFromArcToCorner;

    // Convert radius to geo coordinates for comparison
    const radiusInGeo = renderer.layerToGeoLength(radius);

    // Check if the mouse position is near the arc (within epsilon of the arc radius)
    const distanceFromArc = Math.abs(d - radiusInGeo);

    if (distanceFromArc <= epsilon) {
        // Create vector from angle vertex to mouse position
        const vectorPoint = [posInGeo.x - anglePoint[0], posInGeo.y - anglePoint[1]];

        // Check if point is within the angle's angular range
        if (_pointBelongsToAngle(angle, vectorPoint)) {
            return {
                doc: doc,
                hitDetails: <GeoHitContextDetails>{
                    hitId: angle.relIndex,
                    el: angle,
                    hitMargin: distanceFromArc,
                    epsilon: epsilon,
                },
            };
        }
    }

    return undefined;
}

export function checkHitOnAnglesInside2D(
    doc: GeoDocCtrl,
    els: GeoRenderElement[],
    posInGeo: Position,
    renderer: GeoRenderer
): GeoSelectHitContext[] {
    return els
        .filter(el => doc.editor.filterElementFunc(el))
        .map(el => checkHitInsideAngle2D(posInGeo, el as RenderAngle, doc, renderer))
        .filter(h => h && true);
}

function checkHitInsideAngle2D(
    posInGeo: Position,
    angle: RenderAngle,
    doc: GeoDocCtrl,
    renderer: GeoRenderer
): GeoSelectHitContext {
    const anglePoint = (renderer.elementAt(angle.anglePointIdx) as RenderVertex).coords;

    // Calculate the distance from the mouse position to the angle vertex
    const d = Math.sqrt(Math.pow(anglePoint[0] - posInGeo.x, 2) + Math.pow(anglePoint[1] - posInGeo.y, 2));

    // Use the same radius calculation logic as in the renderer
    const minSideLineSegmenGeotLength = renderer.getGeoMinLengthOfAngleSide(angle);
    const spaceFromArcToCorner = angle.renderProp.spaceFromArcToCorner;
    const radius =
        minSideLineSegmenGeotLength !== undefined
            ? Math.min(spaceFromArcToCorner, renderer.geoToLayerLength(minSideLineSegmenGeotLength))
            : spaceFromArcToCorner;

    // Check if the mouse position is within the angle's arc radius (in layer coordinates)
    const isPosCloserThanAngleArc = renderer.geoToLayerLength(d) <= radius;

    // Create vector from angle vertex to mouse position
    const vectorPoint = [posInGeo.x - anglePoint[0], posInGeo.y - anglePoint[1]];

    // Check if point is within the angle and within the arc radius
    if (_pointBelongsToAngle(angle, vectorPoint) && isPosCloserThanAngleArc) {
        return {
            doc: doc,
            hitDetails: <GeoHitContextDetails>{
                hitId: angle.relIndex,
                el: angle,
                hitMargin: d,
            },
        };
    }

    return undefined;
}

function checkHitOnEllipse2D(
    posInGeo: Position,
    ellipse: RenderEllipse,
    doc: GeoDocCtrl,
    renderer: GeoRenderer,
    epsilon: number
): GeoSelectHitContext {
    const f1 = renderer.elementAt(ellipse.f1Idx) as RenderVertex;
    const f2 = renderer.elementAt(ellipse.f2Idx) as RenderVertex;

    let d =
        distance2Point(f1.coords, [posInGeo.x, posInGeo.y, 0]) + distance2Point(f2.coords, [posInGeo.x, posInGeo.y, 0]);

    d = Math.abs(d - 2 * ellipse.a);

    if (d < epsilon) {
        const hitDetails: GeoHitContextDetails = {
            hitId: ellipse.relIndex,
            el: ellipse,
            hitMargin: d,
            epsilon: epsilon,
        };

        return {
            doc: doc,
            hitDetails: hitDetails,
        };
    }

    return undefined;
}

function checkHitInsideEllipse2D(
    posInGeo: Position,
    ellipse: RenderEllipseShape,
    doc: GeoDocCtrl,
    renderer: GeoRenderer
): GeoSelectHitContext {
    const f1 = renderer.elementAt(ellipse.f1Idx) as RenderVertex;
    const f2 = renderer.elementAt(ellipse.f2Idx) as RenderVertex;

    const d =
        distance2Point(f1.coords, [posInGeo.x, posInGeo.y, 0]) + distance2Point(f2.coords, [posInGeo.x, posInGeo.y, 0]);

    if (d <= 2 * ellipse.a) {
        const hitDetails: GeoHitContextDetails = {
            hitId: ellipse.relIndex,
            el: ellipse,
            hitMargin: d,
        };

        return {
            doc: doc,
            hitDetails: hitDetails,
        };
    }

    return undefined;
}

function cross(p1: RenderVertex, p2: RenderVertex, p3: Position): number {
    return (
        (p2.coords[0] - p1.coords[0]) * (p3.y - p1.coords[1]) - (p3.x - p1.coords[0]) * (p2.coords[1] - p1.coords[1])
    );
}
